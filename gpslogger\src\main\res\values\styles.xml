<resources>

    <!-- Base application theme. -->
    <!--    https://stackoverflow.com/a/52289175/974369 to avoid errors about MeasureSpec.EXACT  -->
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">

        <!-- Set theme colors from http://www.google.com/design/spec/style/color.html#color-color-palette-->
        <!-- colorPrimary is used for the default action bar background -->
        <item name="colorPrimary">@color/primaryColor</item>
        <!-- colorPrimaryDark is used for the status bar -->
        <item name="colorPrimaryDark">@color/primaryColorDark</item>
        <!-- colorAccent is used as the default value for colorControlActivated
             which is used to tint widgets -->
        <item name="colorAccent">@color/accentColor</item>
        <!-- You can also set colorControlNormal, colorControlActivated
             colorControlHighlight and colorSwitchThumbNormal. -->

        <!--Remove shadow below top toolbar/actionbar-->
        <item name="android:windowContentOverlay">@null</item>

        <!--No thin window title-->
        <item name="android:windowNoTitle">true</item>

        <!--No more actionbar, use toolbar instead-->
        <item name="windowActionBar">false</item>

        <!--The preference screens, see <style name="PreferenceThemeOverlay">-->


        <!-- Material Drawer Colors, not all can be set via XML though, some are in code. -->
        <item name="material_drawer_primary_text">@color/primaryColorText</item>
        <item name="material_drawer_secondary_text">@color/secondaryColorText</item>
        <item name="material_drawer_hint_text">@color/primaryColorLight</item>
        <item name="material_drawer_header_selection_text">@color/primaryColorLight</item>
        <item name="material_drawer_selected">@color/divider</item>

        <item name="colorSurface">#FFFFFF</item>
    </style>

    <!-- used by shortcut creation dialog -->
    <style name="Theme.AppCompat.Translucent" parent="AppTheme">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>

    <!--    See values-night/styles.xml for dark theme overridden values.-->
    <color name="primaryColor">#607d8b</color>
    <color name="primaryColorDark">#455a64</color>
    <color name="accentColor">#448aff</color>
    <color name="errorColor">#FF5252</color>
    <color name="warningColor">#ffbd60</color>
    <color name="accentColorComplementary">#4CAF50</color>
    <color name="primaryColorLight">#ffdee7eb</color>
    <color name="primaryColorText">#212121</color>
    <color name="secondaryColorText">#727272</color>
    <color name="icons">#FFFFFF</color>
    <color name="divider">#B6B6B6</color>
    <color name="golden">#ffff8100</color>
    <color name="recording_red">#FF4444</color>


    <color name="colorPrimaryBlack">#000000</color>
    <color name="colorPrimaryBlackDark">#000000</color>
    <color name="colorAccentBlack">#427add</color>
    <color name="surfaceDark">#121212</color>


</resources>
