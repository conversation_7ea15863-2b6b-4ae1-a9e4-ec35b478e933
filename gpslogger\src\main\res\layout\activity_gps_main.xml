<?xml version="1.0" encoding="utf-8"?>

<androidx.drawerlayout.widget.DrawerLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">


    <FrameLayout
        android:id="@+id/supercontainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent" >
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_height="55dp"
            android:layout_width="match_parent"
            android:minHeight="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            android:contentInsetStart="72dp">
            <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                            android:orientation="horizontal"
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content">

                <!-- Recording status indicator -->
                <LinearLayout
                    android:id="@+id/recording_status_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_alignParentLeft="true"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    android:background="@drawable/recording_status_background"
                    android:padding="8dp">

                    <ImageView
                        android:id="@+id/recording_icon"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/ic_mic_recording"
                        android:tint="@color/recording_red"
                        android:layout_marginRight="6dp" />

                    <TextView
                        android:id="@+id/recording_duration_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:text="录音中 00:00"
                        android:textColor="@android:color/white"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <ImageView
                    android:layout_alignParentRight="true"
                    android:contentDescription="GPS fix status"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:padding="5dp"
                    android:layout_centerVertical="true"
                    android:id="@+id/notification_bulb"
                    android:src="@drawable/circle_none"/>


                <ProgressBar
                    android:id="@+id/progressBarGpsFix"
                    android:progressDrawable="@drawable/progress"
                    android:layout_centerVertical="true"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:visibility="invisible"
                    android:indeterminateOnly="true"
                    android:layout_toLeftOf="@id/notification_bulb"/>

            </RelativeLayout>
            </androidx.appcompat.widget.Toolbar>
        <!-- As the main content view, the view below consumes the entire
             space available using match_parent in both dimensions. -->
        <FrameLayout
            android:layout_marginTop="55dp"
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="match_parent" >
        </FrameLayout>
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbarBottom"
            android:layout_gravity="bottom"
            android:layout_height="wrap_content"
            android:layout_width="fill_parent"
            android:visibility="visible"
            android:background="@color/primaryColor"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            />
    </FrameLayout>


</androidx.drawerlayout.widget.DrawerLayout>
