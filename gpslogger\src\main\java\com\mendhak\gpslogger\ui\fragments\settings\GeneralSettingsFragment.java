/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.fragments.settings;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.preference.ListPreference;
import androidx.preference.Preference;
import androidx.preference.PreferenceFragmentCompat;
import androidx.preference.SwitchPreferenceCompat;
import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.PreferenceNames;
import com.mendhak.gpslogger.common.Strings;
import com.mendhak.gpslogger.common.events.AnnotationEvents;

import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.loggers.Files;
import com.mendhak.gpslogger.ui.Dialogs;

import org.slf4j.Logger;

import java.io.File;
import java.util.*;

import de.greenrobot.event.EventBus;
import eltos.simpledialogfragment.SimpleDialog;


public class GeneralSettingsFragment extends PreferenceFragmentCompat implements
        SimpleDialog.OnDialogResultListener,
        Preference.OnPreferenceClickListener,
        Preference.OnPreferenceChangeListener {

    Logger LOG = Logs.of(GeneralSettingsFragment.class);
    private PreferenceHelper preferenceHelper = PreferenceHelper.getInstance();

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        findPreference("enableDisableGps").setOnPreferenceClickListener(this);
        findPreference("debuglogtoemail").setOnPreferenceClickListener(this);

        findPreference(PreferenceNames.APP_THEME_SETTING).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.ANNOTATIONS_BUTTON_COUNT).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.HAPTIC_FEEDBACK_ENABLED).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.HAPTIC_FEEDBACK_INTENSITY).setOnPreferenceChangeListener(this);


        findPreference(PreferenceNames.ANNOTATION_VIEW_MODE).setOnPreferenceChangeListener(this);

        // Voice input settings
        findPreference(PreferenceNames.VOICE_INPUT_ENABLED).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.VOICE_INPUT_LANGUAGE).setOnPreferenceClickListener(this);
        findPreference(PreferenceNames.VOICE_INPUT_TIMEOUT).setOnPreferenceClickListener(this);

        // Audio recording settings
        findPreference(PreferenceNames.AUDIO_RECORDING_SILENCE_TIMEOUT).setOnPreferenceClickListener(this);
        findPreference(PreferenceNames.AUDIO_RECORDING_SILENCE_THRESHOLD).setOnPreferenceClickListener(this);

        // Audio feedback settings
        findPreference("voice_input_button_audio_type").setOnPreferenceChangeListener(this);
        findPreference("voice_input_button_custom_audio_path").setOnPreferenceClickListener(this);
        findPreference("text_input_button_audio_type").setOnPreferenceChangeListener(this);
        findPreference("text_input_button_custom_audio_path").setOnPreferenceClickListener(this);
        findPreference("counter_button_audio_type").setOnPreferenceChangeListener(this);
        findPreference("counter_button_custom_audio_path").setOnPreferenceClickListener(this);
        findPreference("single_click_button_audio_type").setOnPreferenceChangeListener(this);
        findPreference("single_click_button_custom_audio_path").setOnPreferenceClickListener(this);

        setPreferenceVoiceInputSummaries();
        setPreferenceAudioFeedbackSummaries();
        setAudioRecordingSummaries();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            findPreference("resetapp").setOnPreferenceClickListener(this);
        }
        else {
            findPreference("resetapp").setEnabled(false);
        }


        setCoordinatesFormatPreferenceItem();
        setLanguagesPreferenceItem();



        Preference aboutInfo = findPreference("about_version_info");
        try {

            aboutInfo.setTitle("GPSLogger version " + getActivity().getPackageManager().getPackageInfo(getActivity().getPackageName(), 0).versionName);
        } catch (PackageManager.NameNotFoundException e) {
        }
    }

    private void setPreferenceVoiceInputSummaries() {
        // Set voice input language summary
        String language = preferenceHelper.getVoiceInputLanguage();
        if (language.isEmpty()) {
            language = "系统默认";
        }
        findPreference(PreferenceNames.VOICE_INPUT_LANGUAGE).setSummary(language);

        // Set voice input timeout summary
        int timeout = preferenceHelper.getVoiceInputTimeout();
        findPreference(PreferenceNames.VOICE_INPUT_TIMEOUT).setSummary(timeout + " 秒");
    }

    private void setAudioRecordingSummaries() {
        // Set audio recording silence timeout summary
        int silenceTimeout = preferenceHelper.getAudioRecordingSilenceTimeout();
        findPreference(PreferenceNames.AUDIO_RECORDING_SILENCE_TIMEOUT).setSummary(silenceTimeout + " 秒");

        // Set audio recording silence threshold summary
        int silenceThreshold = preferenceHelper.getAudioRecordingSilenceThreshold();
        String thresholdDesc;
        if (silenceThreshold <= 400) {
            thresholdDesc = "高敏感度";
        } else if (silenceThreshold <= 800) {
            thresholdDesc = "中等敏感度";
        } else {
            thresholdDesc = "低敏感度";
        }
        findPreference(PreferenceNames.AUDIO_RECORDING_SILENCE_THRESHOLD).setSummary(
            silenceThreshold + " (" + thresholdDesc + ")");
    }

    private void setPreferenceAudioFeedbackSummaries() {
        // Set audio feedback summaries for each button type
        setAudioTypeSummary("voice_input_button_audio_type", preferenceHelper.getVoiceInputButtonAudioType());
        setAudioTypeSummary("text_input_button_audio_type", preferenceHelper.getTextInputButtonAudioType());
        setAudioTypeSummary("counter_button_audio_type", preferenceHelper.getCounterButtonAudioType());
        setAudioTypeSummary("single_click_button_audio_type", preferenceHelper.getSingleClickButtonAudioType());

        // Set custom file path summaries
        setCustomAudioPathSummary("voice_input_button_custom_audio_path", preferenceHelper.getVoiceInputButtonCustomAudioPath());
        setCustomAudioPathSummary("text_input_button_custom_audio_path", preferenceHelper.getTextInputButtonCustomAudioPath());
        setCustomAudioPathSummary("counter_button_custom_audio_path", preferenceHelper.getCounterButtonCustomAudioPath());
        setCustomAudioPathSummary("single_click_button_custom_audio_path", preferenceHelper.getSingleClickButtonCustomAudioPath());
    }

    private void setAudioTypeSummary(String preferenceKey, String audioType) {
        Preference preference = findPreference(preferenceKey);
        if (preference != null) {
            String summary = getAudioTypeDisplayName(audioType);
            preference.setSummary(summary);
        }
    }

    private void setCustomAudioPathSummary(String preferenceKey, String filePath) {
        Preference preference = findPreference(preferenceKey);
        if (preference != null) {
            if (filePath != null && !filePath.trim().isEmpty()) {
                preference.setSummary(new java.io.File(filePath).getName());
            } else {
                preference.setSummary("未选择文件");
            }
        }
    }

    private String getAudioTypeDisplayName(String audioType) {
        switch (audioType) {
            case "none":
                return "无提示音";
            case "system_default":
                return "系统默认音";
            case "system_notification":
                return "系统通知音";
            case "system_ringtone":
                return "系统铃声";
            case "system_alarm":
                return "系统警告音";
            case "custom_file":
                return "自定义音频文件";
            default:
                return "无提示音";
        }
    }

    @Override
    public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
        setPreferencesFromResource(R.xml.pref_general, rootKey);
    }

    private void setLanguagesPreferenceItem() {
        ListPreference langs = findPreference("changelanguage");

        Map<String,String> localeDisplayNames = Strings.getAvailableLocales(getActivity());

        String[] locales = localeDisplayNames.keySet().toArray(new String[localeDisplayNames.keySet().size()]);
        String[] displayValues = localeDisplayNames.values().toArray(new String[localeDisplayNames.values().size()]);

        langs.setEntries(displayValues);
        langs.setEntryValues(locales);
        langs.setDefaultValue("en");
        langs.setOnPreferenceChangeListener(this);
    }

    private void setCoordinatesFormatPreferenceItem() {
        ListPreference coordFormats = findPreference("coordinatedisplayformat");
        String[] coordinateDisplaySamples = new String[]{"12° 34' 56.7890\" S","12° 34.5678' S","-12.345678"};
        coordFormats.setEntries(coordinateDisplaySamples);
        coordFormats.setEntryValues(new String[]{PreferenceNames.DegreesDisplayFormat.DEGREES_MINUTES_SECONDS.toString(),PreferenceNames.DegreesDisplayFormat.DEGREES_DECIMAL_MINUTES.toString(),PreferenceNames.DegreesDisplayFormat.DECIMAL_DEGREES.toString()});
        coordFormats.setDefaultValue("0");
        coordFormats.setOnPreferenceChangeListener(this);
        coordFormats.setSummary(coordinateDisplaySamples[PreferenceHelper.getInstance().getDisplayLatLongFormat().ordinal()]);
    }



    @Override
    public boolean onPreferenceClick(Preference preference) {

        if (preference.getKey().equals("enableDisableGps")) {
            startActivity(new Intent("android.settings.LOCATION_SOURCE_SETTINGS"));
            return true;
        }

        if(preference.getKey().equalsIgnoreCase(PreferenceNames.VOICE_INPUT_LANGUAGE)){
            eltos.simpledialogfragment.form.SimpleFormDialog.build()
                    .title("语音识别语言")
                    .msg("输入语音识别的语言代码（如zh-CN、en-US），留空使用系统默认")
                    .fields(
                            eltos.simpledialogfragment.form.Input.plain(PreferenceNames.VOICE_INPUT_LANGUAGE)
                                    .hint("语言代码")
                                    .text(preferenceHelper.getVoiceInputLanguage())
                    )
                    .pos(R.string.ok)
                    .neg(R.string.cancel)
                    .show(this, PreferenceNames.VOICE_INPUT_LANGUAGE);
            return true;
        }

        if(preference.getKey().equalsIgnoreCase(PreferenceNames.VOICE_INPUT_TIMEOUT)){
            eltos.simpledialogfragment.form.SimpleFormDialog.build()
                    .title("语音识别超时")
                    .msg("设置语音识别的超时时间（秒）")
                    .fields(
                            eltos.simpledialogfragment.form.Input.plain(PreferenceNames.VOICE_INPUT_TIMEOUT)
                                    .hint("超时时间")
                                    .inputType(android.text.InputType.TYPE_CLASS_NUMBER)
                                    .text(String.valueOf(preferenceHelper.getVoiceInputTimeout()))
                    )
                    .pos(R.string.ok)
                    .neg(R.string.cancel)
                    .show(this, PreferenceNames.VOICE_INPUT_TIMEOUT);
            return true;
        }

        // Handle audio recording silence timeout setting
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.AUDIO_RECORDING_SILENCE_TIMEOUT)){
            eltos.simpledialogfragment.form.SimpleFormDialog.build()
                    .title("静音超时时间")
                    .msg("设置检测到静音后自动停止录音的时间（1-5秒）\n默认3秒，适合大多数语音输入场景")
                    .fields(
                            eltos.simpledialogfragment.form.Input.plain(PreferenceNames.AUDIO_RECORDING_SILENCE_TIMEOUT)
                                    .hint("超时时间（秒）")
                                    .inputType(android.text.InputType.TYPE_CLASS_NUMBER)
                                    .text(String.valueOf(preferenceHelper.getAudioRecordingSilenceTimeout()))
                    )
                    .pos(R.string.ok)
                    .neg(R.string.cancel)
                    .show(this, PreferenceNames.AUDIO_RECORDING_SILENCE_TIMEOUT);
            return true;
        }

        // Handle audio recording silence threshold setting
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.AUDIO_RECORDING_SILENCE_THRESHOLD)){
            eltos.simpledialogfragment.form.SimpleFormDialog.build()
                    .title("静音检测阈值")
                    .msg("设置静音检测的敏感度（200-2000，数值越小越敏感）")
                    .fields(
                            eltos.simpledialogfragment.form.Input.plain(PreferenceNames.AUDIO_RECORDING_SILENCE_THRESHOLD)
                                    .hint("阈值")
                                    .inputType(android.text.InputType.TYPE_CLASS_NUMBER)
                                    .text(String.valueOf(preferenceHelper.getAudioRecordingSilenceThreshold()))
                    )
                    .pos(R.string.ok)
                    .neg(R.string.cancel)
                    .show(this, PreferenceNames.AUDIO_RECORDING_SILENCE_THRESHOLD);
            return true;
        }

        // Handle custom audio file selection
        if(preference.getKey().endsWith("_custom_audio_path")){
            handleCustomAudioFileSelection(preference.getKey());
            return true;
        }

        if (preference.getKey().equals("resetapp")) {
            SimpleDialog.build()
                    .title(getString(R.string.reset_app_title))
                    .msgHtml(getString(R.string.reset_app_summary))
                    .neg(R.string.cancel)
                    .show(this, "RESET_APP");
            return true;
        }

        if(preference.getKey().equals("debuglogtoemail")){
            Intent intent = new Intent(Intent.ACTION_SEND);
            intent.setType("text/plain");
            intent.putExtra(Intent.EXTRA_SUBJECT, "GPSLogger Debug Log");

            StringBuilder diagnostics = new StringBuilder();
            diagnostics.append("Android version: ").append(Build.VERSION.SDK_INT).append("\r\n");
            diagnostics.append("OS version: ").append(System.getProperty("os.version")).append("\r\n");
            diagnostics.append("Manufacturer: ").append(Build.MANUFACTURER).append("\r\n");
            diagnostics.append("Model: ").append(Build.MODEL).append("\r\n");
            diagnostics.append("Product: ").append(Build.PRODUCT).append("\r\n");
            diagnostics.append("Brand: ").append(Build.BRAND).append("\r\n");


            intent.putExtra(Intent.EXTRA_TEXT, diagnostics.toString());
            File root = Files.storageFolder(getActivity());
            File file = new File(root, "/debuglog.txt");
            if (file.exists() && file.canRead()) {
                Uri uri = Uri.parse("file://" + file);
                intent.putExtra(Intent.EXTRA_STREAM, uri);
                startActivity(Intent.createChooser(intent, "Send debug log"));
            }
            else {
                Toast.makeText(getActivity(), "debuglog.txt not found", Toast.LENGTH_LONG).show();
            }

            return true;

        }

        return false;
    }

    @Override
    public boolean onPreferenceChange(Preference preference, Object newValue) {

        if(preference.getKey().equals("changelanguage")){
            PreferenceHelper.getInstance().setUserSpecifiedLocale((String) newValue);
            LOG.debug("Language chosen: " + PreferenceHelper.getInstance().getUserSpecifiedLocale());
            return true;
        }
        if(preference.getKey().equals("coordinatedisplayformat")){
            PreferenceHelper.getInstance().setDisplayLatLongFormat(PreferenceNames.DegreesDisplayFormat.valueOf(newValue.toString()));
            LOG.debug("Coordinate format chosen: " + PreferenceHelper.getInstance().getDisplayLatLongFormat());
            setCoordinatesFormatPreferenceItem();
            return true;
        }
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.APP_THEME_SETTING)){
            Dialogs.alert("", getString(R.string.restart_required), getActivity());
            return true;
        }
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.ANNOTATIONS_BUTTON_COUNT)){
            int buttonCount = (Integer) newValue;
            PreferenceHelper.getInstance().setAnnotationButtonCount(buttonCount);
            LOG.debug("Annotation button count set to: " + buttonCount);

            // Notify annotation view about the change
            EventBus.getDefault().post(new AnnotationEvents.ButtonCountChanged(buttonCount));
            return true;
        }
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.HAPTIC_FEEDBACK_ENABLED)){
            boolean enabled = (Boolean) newValue;
            PreferenceHelper.getInstance().setHapticFeedbackEnabled(enabled);
            LOG.debug("Haptic feedback enabled set to: " + enabled);
            return true;
        }
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.HAPTIC_FEEDBACK_INTENSITY)){
            String intensity = (String) newValue;
            PreferenceHelper.getInstance().setHapticFeedbackIntensity(intensity);
            LOG.debug("Haptic feedback intensity set to: " + intensity);
            return true;
        }



        if(preference.getKey().equalsIgnoreCase(PreferenceNames.ANNOTATION_VIEW_MODE)){
            String viewMode = (String) newValue;
            PreferenceHelper.getInstance().setAnnotationViewMode(viewMode);
            LOG.debug("Annotation view mode set to: " + viewMode);

            // Notify annotation view about the change
            EventBus.getDefault().post(new AnnotationEvents.LayoutStyleChanged(
                PreferenceHelper.getInstance().getAnnotationLayoutStyle(),
                PreferenceHelper.getInstance().getAnnotationSpacingMode(),
                viewMode
            ));
            return true;
        }

        if(preference.getKey().equalsIgnoreCase(PreferenceNames.VOICE_INPUT_ENABLED)){
            boolean enabled = (Boolean) newValue;
            preferenceHelper.setVoiceInputEnabled(enabled);
            LOG.debug("Voice input enabled set to: " + enabled);
            return true;
        }

        // Handle audio feedback type changes
        if(preference.getKey().endsWith("_audio_type")){
            String audioType = (String) newValue;
            handleAudioTypeChange(preference.getKey(), audioType);
            return true;
        }

        return false;
    }

    @Override
    public boolean onResult(@NonNull String dialogTag, int which, @NonNull Bundle extras) {
        if(dialogTag.equalsIgnoreCase("RESET_APP") && which == BUTTON_POSITIVE){
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT){
                    ((ActivityManager) getActivity().getSystemService(Context.ACTIVITY_SERVICE)).clearApplicationUserData();
            }
        }

        if(dialogTag.equalsIgnoreCase(PreferenceNames.VOICE_INPUT_LANGUAGE) && which == BUTTON_POSITIVE){
            String language = extras.getString(PreferenceNames.VOICE_INPUT_LANGUAGE);
            preferenceHelper.setVoiceInputLanguage(language);
            setPreferenceVoiceInputSummaries();
            return true;
        }

        if(dialogTag.equalsIgnoreCase(PreferenceNames.VOICE_INPUT_TIMEOUT) && which == BUTTON_POSITIVE){
            String timeoutStr = extras.getString(PreferenceNames.VOICE_INPUT_TIMEOUT);
            try {
                int timeout = Integer.parseInt(timeoutStr);
                if (timeout > 0 && timeout <= 60) {
                    preferenceHelper.setVoiceInputTimeout(timeout);
                    setPreferenceVoiceInputSummaries();
                } else {
                    android.widget.Toast.makeText(getActivity(), "超时时间必须在1-60秒之间", android.widget.Toast.LENGTH_SHORT).show();
                }
            } catch (NumberFormatException e) {
                android.widget.Toast.makeText(getActivity(), "请输入有效的数字", android.widget.Toast.LENGTH_SHORT).show();
            }
            return true;
        }

        // Handle audio recording silence timeout result
        if(dialogTag.equalsIgnoreCase(PreferenceNames.AUDIO_RECORDING_SILENCE_TIMEOUT) && which == BUTTON_POSITIVE){
            String timeoutStr = extras.getString(PreferenceNames.AUDIO_RECORDING_SILENCE_TIMEOUT);
            try {
                int timeout = Integer.parseInt(timeoutStr);
                if (timeout >= 1 && timeout <= 5) {
                    preferenceHelper.setAudioRecordingSilenceTimeout(timeout);
                    setAudioRecordingSummaries();
                } else {
                    android.widget.Toast.makeText(getActivity(), "静音超时时间必须在1-5秒之间（推荐3秒）", android.widget.Toast.LENGTH_SHORT).show();
                }
            } catch (NumberFormatException e) {
                android.widget.Toast.makeText(getActivity(), "请输入有效的数字", android.widget.Toast.LENGTH_SHORT).show();
            }
            return true;
        }

        // Handle audio recording silence threshold result
        if(dialogTag.equalsIgnoreCase(PreferenceNames.AUDIO_RECORDING_SILENCE_THRESHOLD) && which == BUTTON_POSITIVE){
            String thresholdStr = extras.getString(PreferenceNames.AUDIO_RECORDING_SILENCE_THRESHOLD);
            try {
                int threshold = Integer.parseInt(thresholdStr);
                if (threshold >= 200 && threshold <= 2000) {
                    preferenceHelper.setAudioRecordingSilenceThreshold(threshold);
                    setAudioRecordingSummaries();
                } else {
                    android.widget.Toast.makeText(getActivity(), "静音阈值必须在200-2000之间", android.widget.Toast.LENGTH_SHORT).show();
                }
            } catch (NumberFormatException e) {
                android.widget.Toast.makeText(getActivity(), "请输入有效的数字", android.widget.Toast.LENGTH_SHORT).show();
            }
            return true;
        }

        return false;
    }

    private void handleAudioTypeChange(String preferenceKey, String audioType) {
        // Save the audio type setting
        switch (preferenceKey) {
            case "voice_input_button_audio_type":
                preferenceHelper.setVoiceInputButtonAudioType(audioType);
                break;
            case "text_input_button_audio_type":
                preferenceHelper.setTextInputButtonAudioType(audioType);
                break;
            case "counter_button_audio_type":
                preferenceHelper.setCounterButtonAudioType(audioType);
                break;
            case "single_click_button_audio_type":
                preferenceHelper.setSingleClickButtonAudioType(audioType);
                break;
        }

        // Update summary
        setAudioTypeSummary(preferenceKey, audioType);

        // Test the audio if it's not "none"
        if (!"none".equals(audioType)) {
            testAudioFeedback(audioType, null);
        }

        LOG.debug("Audio type for {} set to: {}", preferenceKey, audioType);
    }

    private void handleCustomAudioFileSelection(String preferenceKey) {
        // Create file picker intent
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.setType("audio/*");
        intent.addCategory(Intent.CATEGORY_OPENABLE);

        try {
            // Store the preference key for later use in onActivityResult
            // We'll use a simple approach with SharedPreferences for now
            getActivity().getSharedPreferences("temp_audio_selection", Context.MODE_PRIVATE)
                    .edit()
                    .putString("pending_preference_key", preferenceKey)
                    .apply();

            startActivityForResult(Intent.createChooser(intent, "选择音频文件"), 1001);
        } catch (android.content.ActivityNotFoundException ex) {
            android.widget.Toast.makeText(getActivity(), "请安装文件管理器", android.widget.Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == 1001 && resultCode == getActivity().RESULT_OK && data != null) {
            android.net.Uri uri = data.getData();
            if (uri != null) {
                // Get the pending preference key
                String preferenceKey = getActivity().getSharedPreferences("temp_audio_selection", Context.MODE_PRIVATE)
                        .getString("pending_preference_key", "");

                if (!preferenceKey.isEmpty()) {
                    String filePath = uri.toString();

                    // Save the custom audio file path
                    switch (preferenceKey) {
                        case "voice_input_button_custom_audio_path":
                            preferenceHelper.setVoiceInputButtonCustomAudioPath(filePath);
                            break;
                        case "text_input_button_custom_audio_path":
                            preferenceHelper.setTextInputButtonCustomAudioPath(filePath);
                            break;
                        case "counter_button_custom_audio_path":
                            preferenceHelper.setCounterButtonCustomAudioPath(filePath);
                            break;
                        case "single_click_button_custom_audio_path":
                            preferenceHelper.setSingleClickButtonCustomAudioPath(filePath);
                            break;
                    }

                    // Update summary
                    setCustomAudioPathSummary(preferenceKey, filePath);

                    // Test the custom audio file
                    testAudioFeedback("custom_file", filePath);

                    // Clear the temporary preference
                    getActivity().getSharedPreferences("temp_audio_selection", Context.MODE_PRIVATE)
                            .edit()
                            .remove("pending_preference_key")
                            .apply();

                    LOG.debug("Custom audio file selected for {}: {}", preferenceKey, filePath);
                }
            }
        }
    }

    private void testAudioFeedback(String audioType, String customFilePath) {
        try {
            com.mendhak.gpslogger.ui.components.AudioFeedbackManager audioManager =
                com.mendhak.gpslogger.ui.components.AudioFeedbackManager.getInstance(getActivity());
            audioManager.testAudio(audioType, customFilePath);
        } catch (Exception e) {
            LOG.error("Error testing audio feedback", e);
            android.widget.Toast.makeText(getActivity(), "音频测试失败", android.widget.Toast.LENGTH_SHORT).show();
        }
    }
}
