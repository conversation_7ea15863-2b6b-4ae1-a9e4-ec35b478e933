/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.fragments.display;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.text.InputType;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.GridLayout;
import android.content.Intent;
import java.util.Objects;

import androidx.annotation.NonNull;

import com.dd.processbutton.iml.ActionProcessButton;
import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.common.EventBusHook;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.Session;
import com.mendhak.gpslogger.common.Strings;
import com.mendhak.gpslogger.common.events.AnnotationEvents;
import com.mendhak.gpslogger.common.events.CommandEvents;
import com.mendhak.gpslogger.common.events.ProfileEvents;
import com.mendhak.gpslogger.common.events.ServiceEvents;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.ui.components.AnnotationLayoutHelper;
import com.mendhak.gpslogger.ui.components.ButtonGroup;
import com.mendhak.gpslogger.ui.components.ButtonGroupManager;
import com.mendhak.gpslogger.ui.components.HapticFeedbackManager;
import com.mendhak.gpslogger.GpsMainActivity;
import com.mendhak.gpslogger.ui.components.LayoutEnums.*;
import com.mendhak.gpslogger.ui.adapters.AnnotationListAdapter;
import com.mendhak.gpslogger.ui.adapters.ButtonGroupAdapter;
import com.mendhak.gpslogger.ui.dialogs.GroupEditDialog;
import com.mendhak.gpslogger.ui.dialogs.GroupButtonManagementDialog;
import com.mendhak.gpslogger.ui.components.VoiceInputManager;
import com.mendhak.gpslogger.ui.components.VoicePermissionManager;
import com.mendhak.gpslogger.ui.components.LayoutEnums.TriggerMode;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.InputType;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;

import java.util.Map;
import java.util.HashMap;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import de.greenrobot.event.EventBus;
import eltos.simpledialogfragment.SimpleDialog;
import eltos.simpledialogfragment.form.ColorField;
import eltos.simpledialogfragment.form.Input;
import eltos.simpledialogfragment.form.SimpleFormDialog;


public class AnnotationViewFragment extends GenericViewFragment implements SimpleDialog.OnDialogResultListener {

    Context context;
    private static final Logger LOG = Logs.of(AnnotationViewFragment.class);
    private final PreferenceHelper preferenceHelper = PreferenceHelper.getInstance();

    List<ButtonWrapper> buttonList = new ArrayList<>();
    ButtonWrapper selectedButton;
    private AnnotationListAdapter listAdapter;
    private ButtonGroupAdapter groupAdapter;
    private RecyclerView recyclerView;
    private GridLayout gridLayout;
    private ButtonGroupManager groupManager;

    // 🔑 新增：保存当前打开的管理对话框引用
    private GroupButtonManagementDialog currentManagementDialog;

    // Voice input components
    private VoiceInputManager voiceInputManager;
    private VoicePermissionManager voicePermissionManager;
    private ButtonWrapper pendingVoiceInputButton;

    // Haptic feedback manager
    private HapticFeedbackManager hapticFeedbackManager;

    // Audio feedback manager
    private com.mendhak.gpslogger.ui.components.AudioFeedbackManager audioFeedbackManager;

    // Temporary storage for button states during refresh
    private Map<Integer, String> savedButtonTexts = new HashMap<>();
    private Map<Integer, String> savedButtonColors = new HashMap<>();

    // Recording status UI components
    private android.widget.TextView recordingStatusText;
    private android.widget.TextView recordingDurationText;
    private android.widget.ProgressBar recordingProgressBar;
    private boolean isShowingRecordingIndicator = false;
    private long recordingStartTime = 0;


    public static class ButtonWrapper {

        String color;
        String text;
        String groupId;                 // 新增：所属分组ID
        int orderInGroup;               // 新增：在分组内的排序
        TriggerMode triggerMode;        // 新增：触发模式
        ActionProcessButton actionButton;

        ButtonWrapper(ActionProcessButton actionButton) {
            this.actionButton = actionButton;
            this.groupId = "default";    // 默认分组
            this.orderInGroup = 0;
            this.triggerMode = TriggerMode.VOICE_INPUT; // 默认语音输入模式
            if (this.actionButton != null) {
                this.actionButton.setMode(ActionProcessButton.Mode.ENDLESS);
            }
        }

        public String getText() {
            return String.valueOf(this.text);
        }

        public void setText(String str) {
            this.text = Strings.cleanDescriptionForJson(str);
            if (this.actionButton != null) {
                this.actionButton.setText(this.text);
            }
        }

        public void setColor(String color) {
            this.color = color;
            if (color == null || "".compareTo(color) == 0) {
                setColor("#808080");
            } else {
                // Check if button has circular background and preserve it
                if (this.actionButton != null) {
                    android.graphics.drawable.Drawable currentBackground = this.actionButton.getBackground();
                    if (currentBackground instanceof android.graphics.drawable.GradientDrawable) {
                        // Preserve the shape and just change the color
                        android.graphics.drawable.GradientDrawable gradientDrawable =
                            (android.graphics.drawable.GradientDrawable) currentBackground;
                        gradientDrawable.setColor(Color.parseColor(this.color));
                    } else {
                        // Regular rectangular button
                        this.actionButton.setBackgroundColor(Color.parseColor(this.color));
                    }
                }
            }
        }

        public String getColor() {
            return this.color;
        }

        // 新增分组相关方法
        public String getGroupId() {
            return groupId;
        }

        public void setGroupId(String groupId) {
            this.groupId = groupId;
        }

        public int getOrderInGroup() {
            return orderInGroup;
        }

        public void setOrderInGroup(int order) {
            this.orderInGroup = order;
        }

        // 新增触发模式相关方法
        public TriggerMode getTriggerMode() {
            return triggerMode != null ? triggerMode : TriggerMode.VOICE_INPUT;
        }

        public void setTriggerMode(TriggerMode triggerMode) {
            this.triggerMode = triggerMode != null ? triggerMode : TriggerMode.VOICE_INPUT;
        }

        // 🔑 关键修复：添加equals和hashCode方法
        // 这样retainAll方法就能正确识别已删除的按钮
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            ButtonWrapper that = (ButtonWrapper) obj;
            return Objects.equals(text, that.text) &&
                   Objects.equals(color, that.color) &&
                   Objects.equals(groupId, that.groupId) &&
                   orderInGroup == that.orderInGroup &&
                   Objects.equals(triggerMode, that.triggerMode);
        }

        @Override
        public int hashCode() {
            return Objects.hash(text, color, groupId, orderInGroup, triggerMode);
        }

        @Override
        public String toString() {
            return "ButtonWrapper{" +
                    "text='" + text + '\'' +
                    ", color='" + color + '\'' +
                    ", groupId='" + groupId + '\'' +
                    ", orderInGroup=" + orderInGroup +
                    ", triggerMode=" + triggerMode +
                    '}';
        }
    }

    private void saveSettings() {
        try {
            if (groupManager != null) {
                groupManager.setButtons(buttonList);
                JSONObject settings = groupManager.toJson();
                preferenceHelper.setAnnotationButtonSettings(settings.toString());
                LOG.debug("Saved annotation settings with " + buttonList.size() + " buttons and " +
                         groupManager.getAllGroups().size() + " groups");
            } else {
                // Fallback to old format if groupManager is not initialized
                saveSettingsLegacyFormat();
            }
        } catch (Exception e) {
            LOG.error("Error saving annotation settings", e);
            // Fallback to legacy format
            saveSettingsLegacyFormat();
        }
    }

    private void saveSettingsLegacyFormat() {
        StringBuilder settings = new StringBuilder("{");
        settings.append("\"version\":1,");
        settings.append("\"buttonCount\":").append(buttonList.size()).append(",");
        settings.append("\"buttons\" : [");
        Iterator<ButtonWrapper> itr = buttonList.iterator();
        int idx = 0;
        while (itr.hasNext()) {
            ButtonWrapper btn = itr.next();
            settings.append("{");
            settings.append("\"idx\":").append(idx).append(",");
            settings.append("\"label\":\"").append(Strings.cleanDescriptionForJson(btn.getText())).append("\",");
            settings.append("\"color\":\"").append(btn.getColor()).append("\",");
            settings.append("\"triggerMode\":\"").append(btn.getTriggerMode().getValue()).append("\"");
            settings.append("}");
            idx++;
            if (itr.hasNext()) {
                settings.append(",");
            }
        }
        settings.append("]");
        settings.append("}");
        preferenceHelper.setAnnotationButtonSettings(settings.toString());
    }

    private void loadSettings() {
        String settings = preferenceHelper.getAnnotationButtonSettings();
        if (settings == null || "".compareTo(settings) == 0) {
            initializeGroupManager();
            return;
        }

        try {
            JSONObject settingsObject = new JSONObject(settings);

            // Check if this is a new format with version info
            int version = settingsObject.optInt("version", 0);
            int savedButtonCount = settingsObject.optInt("buttonCount", 9);

            // Initialize group manager
            initializeGroupManager();

            // Handle different versions
            if (version == 0) {
                LOG.debug("Loading legacy annotation settings format");
                groupManager.migrateFromVersion1(settingsObject);
            } else if (version == 1) {
                LOG.debug("Migrating annotation settings from version 1 to version 2");
                groupManager.migrateFromVersion1(settingsObject);
            } else {
                LOG.debug("Loading annotation settings version " + version + " with " + savedButtonCount + " buttons");

                // 🔑 关键修复：先从JSON恢复完整的按钮列表
                JSONArray buttonArrays = settingsObject.optJSONArray("buttons");
                if (buttonArrays != null && buttonArrays.length() > buttonList.size()) {
                    LOG.debug("Saved button count (" + buttonArrays.length() + ") > current button count (" +
                             buttonList.size() + "), restoring missing buttons");

                    // 扩展buttonList以匹配保存的按钮数量
                    for (int i = buttonList.size(); i < buttonArrays.length(); i++) {
                        ButtonWrapper newButton = new ButtonWrapper(null);
                        buttonList.add(newButton);
                        LOG.debug("Created missing button at index " + i);
                    }
                }

                groupManager.fromJson(settingsObject);
            }

            // 加载按钮数据
            JSONArray buttonArrays = settingsObject.optJSONArray("buttons");
            if (buttonArrays != null) {
                for (int i = 0; i < buttonArrays.length(); i++) {
                    JSONObject btnObj = buttonArrays.getJSONObject(i);
                    int idx = btnObj.getInt("idx");
                    String txt = btnObj.getString("label");
                    String color = btnObj.getString("color");
                    String groupId = btnObj.optString("groupId", "default");
                    int orderInGroup = btnObj.optInt("orderInGroup", i);
                    String triggerModeValue = btnObj.optString("triggerMode", "voice_input");
                    TriggerMode triggerMode = TriggerMode.fromValue(triggerModeValue);

                    // 确保buttonList有足够的空间
                    while (buttonList.size() <= idx) {
                        ButtonWrapper newButton = new ButtonWrapper(null);
                        buttonList.add(newButton);
                        LOG.debug("Extended buttonList to index " + buttonList.size());
                    }

                    if (idx >= 0 && idx < buttonList.size()) {
                        ButtonWrapper btn = buttonList.get(idx);
                        btn.setText(txt);
                        btn.setColor(color);
                        btn.setGroupId(groupId);
                        btn.setOrderInGroup(orderInGroup);
                        btn.setTriggerMode(triggerMode);
                        LOG.debug("Loaded button " + idx + ": '" + txt + "' in group " + groupId);
                    } else {
                        LOG.error("Invalid button index in settings: " + idx);
                    }
                }
            }

        } catch (Exception e) {
            LOG.error("Exception loading annotation settings: " + e.getMessage(), e);
        }

        // 🔑 确保ButtonGroupManager知道所有按钮
        if (groupManager != null) {
            groupManager.setButtons(buttonList);
            LOG.debug("Updated ButtonGroupManager with " + buttonList.size() + " buttons after loading");
        }

        // Restore any saved button states after loading settings
        restoreSavedButtonStates();
    }

    /**
     * Initialize the group manager
     */
    private void initializeGroupManager() {
        if (groupManager == null) {
            groupManager = new ButtonGroupManager();
            groupManager.setButtons(buttonList);
            LOG.debug("Initialized ButtonGroupManager");
        }
    }

    /**
     * Save current button states before refreshing
     */
    private void saveCurrentButtonStates() {
        savedButtonTexts.clear();
        savedButtonColors.clear();

        for (int i = 0; i < buttonList.size(); i++) {
            ButtonWrapper button = buttonList.get(i);
            if (button.text != null && !button.text.isEmpty()) {
                savedButtonTexts.put(i, button.text);
            }
            if (button.color != null && !button.color.isEmpty()) {
                savedButtonColors.put(i, button.color);
            }
        }

        LOG.debug("Saved " + savedButtonTexts.size() + " button texts and " +
                 savedButtonColors.size() + " button colors");
    }

    /**
     * Restore saved button states after refreshing
     */
    private void restoreSavedButtonStates() {
        for (Map.Entry<Integer, String> entry : savedButtonTexts.entrySet()) {
            int index = entry.getKey();
            String text = entry.getValue();
            if (index < buttonList.size()) {
                buttonList.get(index).setText(text);
            }
        }

        for (Map.Entry<Integer, String> entry : savedButtonColors.entrySet()) {
            int index = entry.getKey();
            String color = entry.getValue();
            if (index < buttonList.size()) {
                buttonList.get(index).setColor(color);
            }
        }

        LOG.debug("Restored " + savedButtonTexts.size() + " button texts and " +
                 savedButtonColors.size() + " button colors");

        // Clear saved states after restoration
        savedButtonTexts.clear();
        savedButtonColors.clear();
    }

    public static AnnotationViewFragment newInstance() {
        AnnotationViewFragment fragment = new AnnotationViewFragment();
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Initialize voice permission manager early to avoid registerForActivityResult issues
        voicePermissionManager = new VoicePermissionManager(this);

        // Initialize haptic feedback manager from main activity
        if (getActivity() instanceof GpsMainActivity) {
            hapticFeedbackManager = ((GpsMainActivity) getActivity()).getHapticFeedbackManager();
        }

        // Initialize audio feedback manager
        if (getActivity() != null) {
            audioFeedbackManager = com.mendhak.gpslogger.ui.components.AudioFeedbackManager.getInstance(getActivity());
        }
    }

    @Override
    public boolean onResult(@NonNull String dialogTag, int which, @NonNull Bundle extras) {

        // Handle voice input error dialog
        if ("VOICE_INPUT_ERROR".equals(dialogTag)) {
            if (which == BUTTON_POSITIVE) {
                // User chose to use text input
                fallbackToTextAnnotation();
            } else {
                // User cancelled
                pendingVoiceInputButton = null;
            }
            return true;
        }

        // Handle voice permission dialogs
        if (voicePermissionManager != null && voicePermissionManager.handleDialogResult(dialogTag, which)) {
            return true;
        }

        if (which == BUTTON_POSITIVE) {

            Integer idx = null;
            try {
                idx = Integer.valueOf(dialogTag.substring(3));
            } catch (NumberFormatException ignored) {

            }

            if (idx == null || idx < 0 || idx >= buttonList.size()) {
                LOG.error("Could not find button " + dialogTag);
                return true;
            }

            ButtonWrapper buttonWrapper = buttonList.get(idx);
            String enteredText = extras.getString("annotations");
            //Replace all whitespace and newlines, with single space
            enteredText = enteredText.replaceAll("\\s+"," ");
            int color = extras.getInt("color");
            buttonWrapper.setText(enteredText);
            buttonWrapper.setColor(Strings.getHexColorCodeFromInt(color));
            saveSettings();
            return true;
        }
        return false;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {

        if (getActivity() != null) {
            this.context = getActivity().getApplicationContext();
        }

        // Initialize group manager
        initializeGroupManager();

        // Get view mode preference
        ViewMode viewMode = ViewMode.fromKey(preferenceHelper.getAnnotationViewMode());

        View rootView;
        if (viewMode == ViewMode.GROUPED) {
            rootView = inflater.inflate(R.layout.fragment_annotation_view_list, container, false);
            setupGroupedView(rootView);
        } else {
            rootView = inflater.inflate(R.layout.fragment_annotation_view_dynamic, container, false);
            setupGridView(rootView);
        }

        return rootView;
    }



    /**
     * Setup grid view mode
     */
    private void setupGridView(View rootView) {
        // Create dynamic buttons based on user preference
        createDynamicButtons(rootView);
    }

    /**
     * Setup grouped view mode
     */
    private void setupGroupedView(View rootView) {
        recyclerView = rootView.findViewById(R.id.annotation_buttons_recycler);

        // Get layout style preferences
        LayoutStyle layoutStyle = LayoutStyle.fromKey(preferenceHelper.getAnnotationLayoutStyle());
        SpacingMode spacingMode = SpacingMode.fromKey(preferenceHelper.getAnnotationSpacingMode());
        int customSpacing = preferenceHelper.getAnnotationCustomSpacing();
        int effectiveSpacing = customSpacing > 0 ? customSpacing : spacingMode.spacingDp;

        // Create grouped adapter - use getActivity() to ensure we have a valid Activity Context
        groupAdapter = new ButtonGroupAdapter(getActivity(), groupManager, new ButtonGroupAdapter.OnGroupInteractionListener() {
            @Override
            public void onGroupToggle(String groupId, boolean isCollapsed) {
                // Save collapse state - 不需要再次切换，ButtonGroupAdapter已经切换过了
                saveSettings();
                LOG.debug("Group " + groupId + " toggled to " + (isCollapsed ? "collapsed" : "expanded"));
            }

            @Override
            public void onGroupEdit(String groupId) {
                android.util.Log.e("AnnotationViewFragment", "=== onGroupEdit CALLED === groupId: " + groupId);
                try {
                    showGroupEditDialog(groupId);
                    android.util.Log.e("AnnotationViewFragment", "=== showGroupEditDialog COMPLETED ===");
                } catch (Exception e) {
                    android.util.Log.e("AnnotationViewFragment", "Error in onGroupEdit", e);
                }
            }

            @Override
            public void onGroupDelete(String groupId) {
                showGroupDeleteDialog(groupId);
            }

            @Override
            public void onGroupManage(String groupId) {
                android.util.Log.d("AnnotationViewFragment", "onGroupManage called for groupId: " + groupId);
                showGroupButtonManagementDialog(groupId);
            }

            @Override
            public void onButtonClick(ButtonWrapper wrapper) {
                onBtnClick(wrapper);
            }

            @Override
            public void onButtonLongClick(ButtonWrapper wrapper, int position) {
                showEditDialog(wrapper, position);
            }

            @Override
            public void onGroupMoved(int fromPosition, int toPosition) {
                android.util.Log.d("AnnotationViewFragment", "onGroupMoved: " + fromPosition + " -> " + toPosition);
                // 保存新的分组顺序
                saveSettings();

                // 🔑 关键修复：刷新UI确保分组和按钮网格同步
                if (groupAdapter != null) {
                    groupAdapter.updateGroups();
                    android.util.Log.d("AnnotationViewFragment", "Group adapter updated after move");
                } else {
                    android.util.Log.e("AnnotationViewFragment", "groupAdapter is null in onGroupMoved!");
                }
            }

            @Override
            public void onButtonMoved(ButtonWrapper button, String fromGroupId, String toGroupId, int newPosition) {
                android.util.Log.d("AnnotationViewFragment", "onButtonMoved: " + button.getText() +
                    " from " + fromGroupId + " to " + toGroupId + " at position " + newPosition);

                // 更新按钮的分组信息
                button.setGroupId(toGroupId);
                button.setOrderInGroup(newPosition);

                // 保存设置
                saveSettings();

                // 刷新UI
                if (groupAdapter != null) {
                    groupAdapter.updateGroups();
                }
            }
        });

        recyclerView.setLayoutManager(new LinearLayoutManager(context));
        recyclerView.setAdapter(groupAdapter);

        // 设置ItemTouchHelper支持拖拽排序 - 简化版本
        androidx.recyclerview.widget.ItemTouchHelper itemTouchHelper = new androidx.recyclerview.widget.ItemTouchHelper(
            new androidx.recyclerview.widget.ItemTouchHelper.SimpleCallback(
                androidx.recyclerview.widget.ItemTouchHelper.UP | androidx.recyclerview.widget.ItemTouchHelper.DOWN, 0) {

                @Override
                public boolean onMove(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder,
                                    @NonNull RecyclerView.ViewHolder target) {
                    int fromPosition = viewHolder.getAdapterPosition();
                    int toPosition = target.getAdapterPosition();

                    android.util.Log.e("AnnotationViewFragment", "=== DRAG MOVE === from: " + fromPosition + " to: " + toPosition);

                    // 🔑 修复：只允许从分组头部（偶数位置）开始拖拽
                    if (fromPosition % 2 == 0 && fromPosition != toPosition) {
                        int fromGroupIndex = fromPosition / 2;
                        int toGroupIndex;

                        // 🔑 关键修复：智能计算目标分组索引
                        if (toPosition % 2 == 0) {
                            // 拖拽到分组头部
                            toGroupIndex = toPosition / 2;
                        } else {
                            // 拖拽到按钮网格区域，需要智能判断用户意图
                            if (fromPosition < toPosition) {
                                // 向下拖拽：目标是下一个分组
                                toGroupIndex = (toPosition + 1) / 2;
                            } else {
                                // 向上拖拽：目标是当前分组
                                toGroupIndex = (toPosition - 1) / 2;
                            }
                        }

                        android.util.Log.e("AnnotationViewFragment", "🎯 Calculated move: group " + fromGroupIndex + " to " + toGroupIndex);

                        // 验证目标索引有效性
                        if (groupAdapter != null && fromGroupIndex >= 0 && toGroupIndex >= 0 &&
                            fromGroupIndex < groupAdapter.getGroupCount() && toGroupIndex < groupAdapter.getGroupCount() &&
                            fromGroupIndex != toGroupIndex) {

                            android.util.Log.e("AnnotationViewFragment", "✅ Executing group move");
                            return groupAdapter.moveGroup(fromGroupIndex, toGroupIndex);
                        } else {
                            android.util.Log.e("AnnotationViewFragment", "❌ Invalid move parameters: fromGroup=" + fromGroupIndex +
                                ", toGroup=" + toGroupIndex + ", groupCount=" + (groupAdapter != null ? groupAdapter.getGroupCount() : "null"));
                        }
                    } else {
                        android.util.Log.e("AnnotationViewFragment", "❌ Drag rejected: fromPos=" + fromPosition + " (not header or same position)");
                    }

                    return false;
                }

                @Override
                public void onSwiped(@NonNull RecyclerView.ViewHolder viewHolder, int direction) {
                    // 不支持滑动删除
                }

                @Override
                public boolean isLongPressDragEnabled() {
                    return true; // 启用长按拖拽作为备用方案
                }

                @Override
                public int getMovementFlags(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
                    int position = viewHolder.getAdapterPosition();

                    android.util.Log.e("AnnotationViewFragment", "=== MOVEMENT FLAGS === pos: " + position +
                        ", type: " + viewHolder.getClass().getSimpleName());

                    // 简化：只检查位置
                    if (position >= 0 && position % 2 == 0) {
                        android.util.Log.e("AnnotationViewFragment", "Allowing drag for position " + position);
                        return makeMovementFlags(androidx.recyclerview.widget.ItemTouchHelper.UP |
                                               androidx.recyclerview.widget.ItemTouchHelper.DOWN, 0);
                    }

                    android.util.Log.e("AnnotationViewFragment", "No drag for position " + position);
                    return 0;
                }
            }
        );
        itemTouchHelper.attachToRecyclerView(recyclerView);
        groupAdapter.setItemTouchHelper(itemTouchHelper);

        // 启用分组拖拽排序功能
        // 拖拽通过拖拽手柄触发，长按分组标题显示上下文菜单
        groupAdapter.setDragEnabled(true);

        // 强制刷新以确保拖拽手柄显示
        android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
        handler.post(() -> {
            groupAdapter.notifyDataSetChanged();
            android.util.Log.e("AnnotationViewFragment", "=== FORCE REFRESH COMPLETED ===");
        });

        android.util.Log.e("AnnotationViewFragment", "=== GROUP DRAG SORTING ENABLED ===");

        // Create buttons for grouped view
        createButtonsForGroupedView();
    }

    /**
     * Create dynamic annotation buttons based on user preference
     */
    private void createDynamicButtons(View rootView) {
        gridLayout = rootView.findViewById(R.id.annotation_buttons_grid);
        if (gridLayout == null) {
            LOG.error("Could not find annotation_buttons_grid in layout");
            return;
        }

        // Get button count from preferences
        int buttonCount = preferenceHelper.getAnnotationButtonCount();
        LOG.debug("Creating " + buttonCount + " annotation buttons");

        // Get layout style preferences
        LayoutStyle layoutStyle = LayoutStyle.fromKey(preferenceHelper.getAnnotationLayoutStyle());
        SpacingMode spacingMode = SpacingMode.fromKey(preferenceHelper.getAnnotationSpacingMode());
        ViewMode viewMode = ViewMode.fromKey(preferenceHelper.getAnnotationViewMode());
        int customSpacing = preferenceHelper.getAnnotationCustomSpacing();

        LOG.debug("Layout preferences: style=" + layoutStyle.key + ", spacing=" + spacingMode.key +
                 ", custom=" + customSpacing + "dp, view=" + viewMode.key);

        // Calculate optimal layout
        AnnotationLayoutHelper.LayoutConfig layoutConfig;
        try {
            if (AnnotationLayoutHelper.isLandscape(context)) {
                layoutConfig = AnnotationLayoutHelper.calculateLandscapeLayout(context, buttonCount);
                // Apply style preferences to landscape layout
                int effectiveSpacing = customSpacing > 0 ? customSpacing : spacingMode.spacingDp;
                layoutConfig = new AnnotationLayoutHelper.LayoutConfig(
                    layoutConfig.columns, layoutConfig.rows, layoutConfig.totalButtons,
                    layoutStyle, effectiveSpacing, viewMode);
            } else {
                layoutConfig = AnnotationLayoutHelper.calculateOptimalLayout(context, buttonCount,
                                                                           layoutStyle, spacingMode, viewMode, customSpacing);
            }
            LOG.debug("Layout: " + layoutConfig.columns + " columns, " + layoutConfig.rows + " rows, " +
                     "style=" + layoutConfig.layoutStyle.key + ", spacing=" + layoutConfig.spacingDp + "dp");
        } catch (Exception e) {
            LOG.error("Error calculating layout, using fallback", e);
            int fallbackSpacing = customSpacing > 0 ? customSpacing : spacingMode.spacingDp;
            layoutConfig = new AnnotationLayoutHelper.LayoutConfig(3, (int) Math.ceil(buttonCount / 3.0), buttonCount,
                                                                   layoutStyle, fallbackSpacing, viewMode);
        }

        // Configure grid layout
        gridLayout.setColumnCount(layoutConfig.columns);
        gridLayout.setRowCount(layoutConfig.rows);

        // Clear existing buttons
        buttonList.clear();
        gridLayout.removeAllViews();

        // Calculate button dimensions for better space utilization
        int availableHeight = getAvailableHeight();
        int buttonHeight = calculateOptimalButtonHeight(layoutConfig.rows, availableHeight);

        // Create buttons dynamically
        for (int i = 0; i < buttonCount; i++) {
            try {
                ActionProcessButton button = createAnnotationButton(i, layoutConfig.layoutStyle);
                if (button == null) {
                    LOG.error("Failed to create button " + i);
                    continue;
                }

                // Configure grid layout parameters based on layout configuration
                GridLayout.LayoutParams params = new GridLayout.LayoutParams();
                params.width = 0;
                params.height = buttonHeight;
                params.columnSpec = GridLayout.spec(GridLayout.UNDEFINED, 1f);
                params.rowSpec = GridLayout.spec(GridLayout.UNDEFINED, 1f);
                params.setMargins(layoutConfig.spacingDp, layoutConfig.spacingDp,
                                 layoutConfig.spacingDp, layoutConfig.spacingDp);

                // Set button properties for better appearance
                button.setMinHeight(buttonHeight);
                button.setMaxHeight(buttonHeight);
                button.setLayoutParams(params);
                gridLayout.addView(button);

                ButtonWrapper wrapper = new ButtonWrapper(button);
                buttonList.add(wrapper);
            } catch (Exception e) {
                LOG.error("Error creating button " + i, e);
            }
        }

        // Initialize button properties and event handlers
        initializeButtons();
    }

    /**
     * Create a single annotation button
     */
    private ActionProcessButton createAnnotationButton(int index, LayoutStyle layoutStyle) {
        ActionProcessButton button = new ActionProcessButton(context);
        button.setId(View.generateViewId());

        // Apply layout style specific settings
        applyLayoutStyle(button, layoutStyle);

        return button;
    }

    /**
     * Apply layout style specific settings to button
     */
    private void applyLayoutStyle(ActionProcessButton button, LayoutStyle layoutStyle) {
        LOG.debug("Applying layout style: " + layoutStyle.key + " to button");
        switch (layoutStyle) {
            case CIRCULAR:
                LOG.debug("Creating circular button");
                createCircularButton(button);
                break;
            case RECTANGULAR:
            default:
                LOG.debug("Creating rectangular button");
                createRectangularButton(button);
                break;
        }
    }

    /**
     * Create circular button style
     */
    private void createCircularButton(ActionProcessButton button) {
        // Create circular background
        android.graphics.drawable.GradientDrawable circularBackground =
            new android.graphics.drawable.GradientDrawable();
        circularBackground.setShape(android.graphics.drawable.GradientDrawable.OVAL);
        circularBackground.setColor(android.graphics.Color.GRAY);
        circularBackground.setStroke(dpToPx(1), android.graphics.Color.LTGRAY);

        button.setBackground(circularBackground);

        // Adjust text size for circular buttons
        button.setTextSize(android.util.TypedValue.COMPLEX_UNIT_SP, 10);
        button.setMaxLines(2);
        button.setEllipsize(android.text.TextUtils.TruncateAt.END);

        // Add padding for circular buttons
        int padding = dpToPx(4);
        button.setPadding(padding, padding, padding, padding);

        // Ensure button is square for proper circular appearance
        button.post(() -> {
            ViewGroup.LayoutParams params = button.getLayoutParams();
            if (params instanceof GridLayout.LayoutParams) {
                GridLayout.LayoutParams gridParams = (GridLayout.LayoutParams) params;
                // Make width equal to height for circular appearance
                int size = Math.min(gridParams.width, gridParams.height);
                if (size > 0) {
                    gridParams.width = size;
                    gridParams.height = size;
                    button.setLayoutParams(gridParams);
                }
            }
        });
    }

    /**
     * Create rectangular button style
     */
    private void createRectangularButton(ActionProcessButton button) {
        // Set selectable item background using theme attribute
        if (getActivity() != null) {
            try {
                // Get the theme attribute value
                android.util.TypedValue typedValue = new android.util.TypedValue();
                if (getActivity().getTheme().resolveAttribute(android.R.attr.selectableItemBackground, typedValue, true)) {
                    if (typedValue.resourceId != 0) {
                        button.setBackgroundResource(typedValue.resourceId);
                    } else {
                        // Use the raw value if it's not a resource reference
                        button.setBackgroundColor(typedValue.data);
                    }
                }
            } catch (Exception e) {
                LOG.debug("Could not set selectable background: " + e.getMessage());
                // Fallback: set a simple ripple effect manually
                setDefaultButtonBackground(button);
            }
        } else {
            setDefaultButtonBackground(button);
        }
    }

    /**
     * Convert dp to pixels
     */
    private int dpToPx(int dp) {
        if (context == null) return dp;
        return Math.round(dp * context.getResources().getDisplayMetrics().density);
    }

    /**
     * Set a default background for buttons when theme attribute fails
     */
    private void setDefaultButtonBackground(ActionProcessButton button) {
        try {
            // Try to set a simple background with ripple effect
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                // For API 21+, we can create a simple ripple drawable
                button.setBackgroundColor(0x00000000); // Transparent background
                button.setClickable(true);
                button.setFocusable(true);
            } else {
                // For older versions, just use a simple background
                button.setBackgroundColor(0x10000000); // Very light gray
            }
        } catch (Exception e) {
            LOG.debug("Could not set default background: " + e.getMessage());
        }
    }

    /**
     * Calculate available height for buttons
     */
    private int getAvailableHeight() {
        if (getActivity() == null) return 600; // Default fallback

        try {
            android.util.DisplayMetrics displayMetrics = new android.util.DisplayMetrics();
            getActivity().getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);

            // Calculate available height (screen height - toolbar - navigation - padding)
            int screenHeight = displayMetrics.heightPixels;
            int toolbarHeight = 150; // Approximate toolbar height in pixels
            int navigationHeight = 150; // Approximate navigation height in pixels
            int padding = 120; // Additional padding

            return Math.max(400, screenHeight - toolbarHeight - navigationHeight - padding);
        } catch (Exception e) {
            LOG.debug("Could not calculate available height: " + e.getMessage());
            return 600; // Fallback
        }
    }

    /**
     * Calculate optimal button height based on available space and row count
     */
    private int calculateOptimalButtonHeight(int rows, int availableHeight) {
        if (rows <= 0) return 120; // Default button height

        // Calculate height per row, with minimum and maximum constraints
        int heightPerRow = availableHeight / rows;
        int minButtonHeight = 80;  // Minimum button height in pixels
        int maxButtonHeight = 200; // Maximum button height in pixels

        // Apply constraints
        heightPerRow = Math.max(minButtonHeight, heightPerRow);
        heightPerRow = Math.min(maxButtonHeight, heightPerRow);

        // Subtract margins (6px total per button: 3px top + 3px bottom)
        return Math.max(minButtonHeight, heightPerRow - 6);
    }

    /**
     * Initialize button properties and event handlers
     */
    private void initializeButtons() {
        AnnotationViewFragment fragment = this;

        // Get current layout style
        LayoutStyle layoutStyle = LayoutStyle.fromKey(preferenceHelper.getAnnotationLayoutStyle());

        for (int i = 0; i < buttonList.size(); i++) {
            ButtonWrapper buttonWrapper = buttonList.get(i);

            // Skip initialization if actionButton is null (list view mode)
            if (buttonWrapper.actionButton == null) {
                continue;
            }

            // Re-apply layout style before setting color
            applyLayoutStyle(buttonWrapper.actionButton, layoutStyle);

            // Only set default text and color if they haven't been set yet
            if (buttonWrapper.text == null || buttonWrapper.text.isEmpty()) {
                buttonWrapper.setText(getString(R.string.txt_annotation).replace(":", " ") + i);
            }
            if (buttonWrapper.color == null || buttonWrapper.color.isEmpty()) {
                buttonWrapper.setColor("#808080");
            }

            final int btnIdx = i;
            buttonWrapper.actionButton.setOnLongClickListener(v -> {
                SimpleFormDialog.build()
                        .pos(R.string.ok)
                        .neg(R.string.cancel)
                        .title(R.string.annotation_edit_button_label)
                        .fields(
                                Input.plain("annotations")
                                        .hint(R.string.letters_numbers)
                                        .inputType(InputType.TYPE_TEXT_FLAG_MULTI_LINE)
                                        .text(String.valueOf(buttonWrapper.getText())),
                                ColorField.picker("color")
                                        .label(R.string.annotation_edit_button_color).allowCustom(true)
                                        .color(Color.parseColor(buttonWrapper.getColor()))
                        ).show(fragment, "btn" + btnIdx);
                return true;
            });
            buttonWrapper.actionButton.setOnClickListener((b) -> onBtnClick(buttonWrapper));
        }

        // Load saved settings first, then apply any missing defaults
        loadSettings();

        // Apply defaults for any buttons that still don't have text/color after loading
        for (int i = 0; i < buttonList.size(); i++) {
            ButtonWrapper buttonWrapper = buttonList.get(i);
            if (buttonWrapper.actionButton == null) {
                continue;
            }

            // Set default text if still empty after loading settings
            if (buttonWrapper.text == null || buttonWrapper.text.isEmpty()) {
                buttonWrapper.setText(getString(R.string.txt_annotation).replace(":", " ") + i);
            }
            // Set default color if still empty after loading settings
            if (buttonWrapper.color == null || buttonWrapper.color.isEmpty()) {
                buttonWrapper.setColor("#808080");
            }

            // Ensure the UI reflects the current text and color
            buttonWrapper.setText(buttonWrapper.getText());
            buttonWrapper.setColor(buttonWrapper.getColor());
        }
    }

    /**
     * Create buttons for grouped view mode
     */
    private void createButtonsForGroupedView() {
        // Get button count from preferences
        int buttonCount = preferenceHelper.getAnnotationButtonCount();
        LOG.debug("Creating " + buttonCount + " annotation buttons for grouped view");

        // Clear existing buttons
        buttonList.clear();

        // Create button wrappers for grouped view (ActionProcessButton will be created by adapter)
        for (int i = 0; i < buttonCount; i++) {
            ButtonWrapper wrapper = new ButtonWrapper(null); // ActionProcessButton created by adapter
            // Don't set default text here - let loadSettings() handle it
            // Group assignment will be handled by groupManager
            buttonList.add(wrapper);
        }

        // Update group manager with new buttons
        if (groupManager != null) {
            groupManager.setButtons(buttonList);
        }

        // Initialize button properties
        initializeButtonsForGroupedView();
    }

    /**
     * Initialize button properties for grouped view
     */
    private void initializeButtonsForGroupedView() {
        // 🔥 超强力启动日志
        System.out.println("🔧 GPSLOGGER: INITIALIZING BUTTONS FOR GROUPED VIEW");
        android.util.Log.e("GPSLOGGER_INIT", "🔧 INITIALIZING BUTTONS FOR GROUPED VIEW");
        android.util.Log.wtf("GPSLOGGER_CRITICAL", "🔧 INITIALIZING BUTTONS FOR GROUPED VIEW");

        // 🔑 关键修复：确保 ButtonGroupManager 知道所有按钮
        groupManager.setButtons(buttonList);
        android.util.Log.e("AnnotationViewFragment", "📊 Synced " + buttonList.size() + " buttons to GroupManager");

        // 🔑 关键修复：智能分配按钮到不同分组
        List<ButtonGroup> availableGroups = groupManager.getAllGroups();
        android.util.Log.e("AnnotationViewFragment", "📊 Available groups: " + availableGroups.size());

        for (int i = 0; i < buttonList.size(); i++) {
            ButtonWrapper buttonWrapper = buttonList.get(i);
            buttonWrapper.setText(getString(R.string.txt_annotation).replace(":", " ") + i);
            buttonWrapper.setColor("#808080");

            // 🔑 关键修复：智能分配按钮到分组，而不是全部放到默认分组
            if (buttonWrapper.getGroupId() == null || buttonWrapper.getGroupId().isEmpty()) {
                // 🎯 智能分配策略：将按钮均匀分配到所有分组
                if (availableGroups.size() > 1) {
                    // 如果有多个分组，按顺序分配
                    int groupIndex = i % availableGroups.size();
                    ButtonGroup targetGroup = availableGroups.get(groupIndex);
                    buttonWrapper.setGroupId(targetGroup.getId());

                    android.util.Log.e("AnnotationViewFragment", "🎯 Button " + i + " ('" + buttonWrapper.getText() +
                        "') assigned to group: " + targetGroup.getName() + " (ID: " + targetGroup.getId() + ")");
                } else {
                    // 如果只有默认分组，分配到默认分组
                    buttonWrapper.setGroupId("default");
                    android.util.Log.e("AnnotationViewFragment", "📍 Button " + i + " assigned to default group (only group available)");
                }

                // 🔑 关键修复：先将按钮添加到管理器，然后设置顺序
                groupManager.addButtonToGroup(buttonWrapper);

                // 设置在分组内的顺序
                List<ButtonWrapper> groupButtons = groupManager.getButtonsInGroup(buttonWrapper.getGroupId());
                buttonWrapper.setOrderInGroup(groupButtons.size() - 1); // -1 因为按钮已经添加了
            } else {
                android.util.Log.e("AnnotationViewFragment", "✅ Button " + i + " already has group: " + buttonWrapper.getGroupId());
            }
        }

        // Load saved settings to override defaults
        loadSettings();

        // Update adapter if it exists
        if (groupAdapter != null) {
            groupAdapter.updateGroups();
        }

        android.util.Log.e("AnnotationViewFragment", "🔧 BUTTON INITIALIZATION COMPLETED");

        // 🔍 最终诊断：显示所有分组和按钮的分配情况
        diagnoseFinalButtonAssignment();
    }

    /**
     * 诊断最终的按钮分配情况
     */
    private void diagnoseFinalButtonAssignment() {
        // 🔥 超强力日志 - 确保能在任何日志查看器中看到
        System.out.println("🔍 === GPSLOGGER FINAL BUTTON ASSIGNMENT DIAGNOSIS ===");
        android.util.Log.e("GPSLOGGER_DIAGNOSIS", "🔍 === FINAL BUTTON ASSIGNMENT DIAGNOSIS ===");
        android.util.Log.wtf("GPSLOGGER_CRITICAL", "🔍 === FINAL BUTTON ASSIGNMENT DIAGNOSIS ===");

        List<ButtonGroup> allGroups = groupManager.getAllGroups();
        System.out.println("📊 GPSLOGGER: Total groups: " + allGroups.size());
        android.util.Log.e("GPSLOGGER_DIAGNOSIS", "📊 Total groups: " + allGroups.size());
        android.util.Log.wtf("GPSLOGGER_CRITICAL", "📊 Total groups: " + allGroups.size());

        for (int i = 0; i < allGroups.size(); i++) {
            ButtonGroup group = allGroups.get(i);
            List<ButtonWrapper> groupButtons = groupManager.getButtonsInGroup(group.getId());

            String groupInfo = "📁 Group " + i + ": " + group.getName() +
                " (ID: " + group.getId() + ") - " + groupButtons.size() + " buttons";

            System.out.println("GPSLOGGER: " + groupInfo);
            android.util.Log.e("GPSLOGGER_DIAGNOSIS", groupInfo);
            android.util.Log.wtf("GPSLOGGER_CRITICAL", groupInfo);

            for (int j = 0; j < Math.min(5, groupButtons.size()); j++) {
                ButtonWrapper btn = groupButtons.get(j);
                String buttonInfo = "  📍 Button " + j + ": '" + btn.getText() +
                    "' (group: " + btn.getGroupId() + ", order: " + btn.getOrderInGroup() + ")";

                System.out.println("GPSLOGGER: " + buttonInfo);
                android.util.Log.e("GPSLOGGER_DIAGNOSIS", buttonInfo);
                android.util.Log.wtf("GPSLOGGER_CRITICAL", buttonInfo);
            }
        }

        System.out.println("🔍 === GPSLOGGER END DIAGNOSIS ===");
        android.util.Log.e("GPSLOGGER_DIAGNOSIS", "🔍 === END DIAGNOSIS ===");
        android.util.Log.wtf("GPSLOGGER_CRITICAL", "🔍 === END DIAGNOSIS ===");
    }

    private void onBtnClick(ButtonWrapper wrapper) {
        LOG.info("Annotation button clicked: {} with trigger mode: {}",
                wrapper.getText(), wrapper.getTriggerMode().getDisplayName());

        // Provide haptic feedback for button click
        if (hapticFeedbackManager != null) {
            hapticFeedbackManager.performButtonClickFeedback();
        }

        TriggerMode triggerMode = wrapper.getTriggerMode();

        switch (triggerMode) {
            case VOICE_INPUT:
                handleVoiceInputMode(wrapper);
                break;

            case TEXT_INPUT:
                handleTextInputMode(wrapper);
                break;

            case COUNTER_ONLY:
                handleCounterOnlyMode(wrapper);
                break;

            default:
                // Fallback to voice input for unknown modes
                LOG.warn("Unknown trigger mode: {}, falling back to voice input", triggerMode);
                handleVoiceInputMode(wrapper);
                break;
        }
    }

    /**
     * Handle voice input mode
     */
    private void handleVoiceInputMode(ButtonWrapper wrapper) {
        LOG.debug("Handling voice input mode for button: {}", wrapper.getText());

        // Play audio feedback for voice input button
        if (audioFeedbackManager != null) {
            audioFeedbackManager.playButtonFeedback(com.mendhak.gpslogger.ui.components.AudioFeedbackManager.BUTTON_TYPE_VOICE_INPUT);
        }

        // Check if voice input is enabled in settings
        if (preferenceHelper.isVoiceInputEnabled()) {
            LOG.debug("Voice input is enabled, attempting voice input");
            startVoiceInputForButton(wrapper);
        } else {
            // Voice input disabled, start audio recording instead
            LOG.debug("Voice input disabled, starting audio recording mode");
            startVoiceInputForButton(wrapper); // This will trigger audio recording via VoiceInputManager
        }
    }

    /**
     * Handle text input mode
     */
    private void handleTextInputMode(ButtonWrapper wrapper) {
        LOG.debug("Handling text input mode for button: {}", wrapper.getText());

        // Play audio feedback for text input button
        if (audioFeedbackManager != null) {
            audioFeedbackManager.playButtonFeedback(com.mendhak.gpslogger.ui.components.AudioFeedbackManager.BUTTON_TYPE_TEXT_INPUT);
        }

        if (getContext() == null) {
            LOG.error("Context is null, cannot show text input dialog");
            return;
        }

        // Show text input dialog
        com.mendhak.gpslogger.ui.dialogs.TextInputDialog.show(
            getContext(),
            wrapper.getText(),
            new com.mendhak.gpslogger.ui.dialogs.TextInputDialog.OnTextInputListener() {
                @Override
                public void onTextInputResult(String inputText) {
                    LOG.info("Text input result: '{}'", inputText);

                    // Store input text for template variable and clear voice text for mutual exclusion
                    com.mendhak.gpslogger.ui.components.template.providers.BasicVariableProvider
                        .setInputText(getContext(), inputText);

                    // Clear voice text to ensure mutual exclusion between input_text and voice_text variables
                    com.mendhak.gpslogger.common.Session.getInstance().clearTemplateVoiceText();

                    // Set as selected button and post annotation event
                    selectedButton = wrapper;

                    // Ensure GpsLoggingService is running
                    if (getActivity() != null) {
                        getActivity().startService(new android.content.Intent(getActivity(),
                            com.mendhak.gpslogger.GpsLoggingService.class));
                    }

                    // Post annotation event with template context
                    String buttonName = wrapper.getText();
                    int buttonIndex = getButtonIndex(wrapper);
                    String groupName = getButtonGroupName(wrapper);

                    // For text input, only pass inputText to annotation parameter, voiceText should be null
                    // This ensures {input_text} and {voice_text} variables are properly separated
                    EventBus.getDefault().post(new CommandEvents.Annotate(inputText, null,
                                                                          buttonName, buttonIndex, groupName));
                    LOG.info("Posted CommandEvents.Annotate with input text: '{}', voiceText: null, button: '{}', group: '{}'",
                            inputText, buttonName, groupName);
                }

                @Override
                public void onTextInputCancelled() {
                    LOG.debug("Text input cancelled for button: {}", wrapper.getText());
                }
            }
        );
    }

    /**
     * Handle counter only mode
     */
    private void handleCounterOnlyMode(ButtonWrapper wrapper) {
        LOG.debug("Handling counter only mode for button: {}", wrapper.getText());

        // Play audio feedback for counter button
        if (audioFeedbackManager != null) {
            audioFeedbackManager.playButtonFeedback(com.mendhak.gpslogger.ui.components.AudioFeedbackManager.BUTTON_TYPE_COUNTER);
        }

        // Set as selected button
        selectedButton = wrapper;

        // Ensure GpsLoggingService is running
        if (getActivity() != null) {
            getActivity().startService(new android.content.Intent(getActivity(),
                com.mendhak.gpslogger.GpsLoggingService.class));
        }

        // Post annotation event with empty content but counter-only flag
        String buttonName = wrapper.getText();
        int buttonIndex = getButtonIndex(wrapper);
        String groupName = getButtonGroupName(wrapper);

        EventBus.getDefault().post(new CommandEvents.Annotate("", "",
                                                              buttonName, buttonIndex, groupName, true));
        LOG.info("Posted CommandEvents.Annotate for counter only mode, button: '{}', group: '{}', isCounterOnly: true",
                buttonName, groupName);
    }

    /**
     * Start voice input for the specified button
     */
    private void startVoiceInputForButton(ButtonWrapper wrapper) {
        if (wrapper == null) {
            LOG.warn("ButtonWrapper is null, cannot start voice input");
            return;
        }

        if (getActivity() == null) {
            LOG.warn("Activity is null, cannot start voice input");
            return;
        }

        pendingVoiceInputButton = wrapper;

        // Initialize voice input manager if needed
        if (voiceInputManager == null) {
            voiceInputManager = new VoiceInputManager(this, new VoiceInputManager.VoiceInputListener() {
                @Override
                public void onVoiceInputStarted() {
                    LOG.debug("Voice input started");
                    showRecordingIndicator(true, "语音识别中...");
                }

                @Override
                public void onVoiceInputResult(String text) {
                    LOG.debug("Voice input result: " + text);
                    hideRecordingIndicator();
                    handleVoiceInputResult(text);
                }

                @Override
                public void onVoiceInputError(String error) {
                    LOG.warn("Voice input error: " + error);
                    hideRecordingIndicator();
                    handleVoiceInputError(error);
                }

                @Override
                public void onVoiceInputCancelled() {
                    LOG.debug("Voice input cancelled");
                    hideRecordingIndicator();
                    handleVoiceInputCancelled();
                }

                @Override
                public void onAudioRecordingStarted() {
                    LOG.debug("Audio recording started");
                    showRecordingIndicator(true, "录音中...");
                }

                @Override
                public void onAudioRecordingFinished(String audioFilePath) {
                    LOG.info("Audio recording finished: {}", audioFilePath);
                    hideRecordingIndicator();
                    // Hide recording status in main activity
                    if (getActivity() instanceof GpsMainActivity) {
                        ((GpsMainActivity) getActivity()).updateQuickVoiceInputButtonState(false, 0);
                    }
                    handleAudioRecordingFinished(audioFilePath);
                }

                @Override
                public void onAudioRecordingError(String error) {
                    LOG.warn("Audio recording error: {}", error);
                    hideRecordingIndicator();
                    // Hide recording status in main activity
                    if (getActivity() instanceof GpsMainActivity) {
                        ((GpsMainActivity) getActivity()).updateQuickVoiceInputButtonState(false, 0);
                    }
                    handleAudioRecordingError(error);
                }

                @Override
                public void onAudioRecordingCancelled() {
                    LOG.debug("Audio recording cancelled");
                    hideRecordingIndicator();
                    // Hide recording status in main activity
                    if (getActivity() instanceof GpsMainActivity) {
                        ((GpsMainActivity) getActivity()).updateQuickVoiceInputButtonState(false, 0);
                    }
                    handleAudioRecordingCancelled();
                }

                @Override
                public void onRecordingProgress(long durationMs) {
                    updateRecordingDuration(durationMs);
                }

                @Override
                public void onSilenceDetected(long silenceDurationMs) {
                    LOG.debug("Silence detected for {}ms", silenceDurationMs);
                    showRecordingIndicator(true, "检测到静音，即将停止录音...");
                }
            });
        }

        // Permission manager should already be initialized in onCreate

        // Check permissions and start voice input with context
        if (voiceInputManager.hasRecordAudioPermission()) {
            startVoiceInputWithContext(wrapper);
        } else {
            // Request permission first
            voicePermissionManager.requestRecordAudioPermission(new VoicePermissionManager.PermissionCallback() {
                @Override
                public void onPermissionGranted() {
                    LOG.debug("RECORD_AUDIO permission granted, starting voice input");
                    startVoiceInputWithContext(wrapper);
                }

                @Override
                public void onPermissionDenied() {
                    LOG.warn("RECORD_AUDIO permission denied, falling back to text annotation");
                    fallbackToTextAnnotation();
                }

                @Override
                public void onPermissionPermanentlyDenied() {
                    LOG.warn("RECORD_AUDIO permission permanently denied");
                    fallbackToTextAnnotation();
                }
            });
        }
    }

    /**
     * Handle successful voice input result
     */
    private void handleVoiceInputResult(String text) {
        if (pendingVoiceInputButton != null && text != null && !text.trim().isEmpty()) {
            LOG.info("=== VOICE INPUT SUCCESS ===");
            LOG.info("Voice input text: '" + text + "'");
            LOG.info("Trimmed text: '" + text.trim() + "'");
            LOG.info("Button name: '" + pendingVoiceInputButton.getText() + "'");

            // 🔑 关键修复：不要将语音内容设置为按钮文本
            // 按钮文本应该始终保持为按钮名称，语音内容只用于注释
            // pendingVoiceInputButton.setText(text.trim()); // 删除这行错误代码

            // 不需要保存设置，因为按钮文本没有改变
            // saveSettings(); // 删除这行代码

            LOG.info("Button text remains unchanged: '" + pendingVoiceInputButton.getText() + "'");

            // Set as selected button and post annotation event
            selectedButton = pendingVoiceInputButton;
            LOG.info("Selected button set to: '" + selectedButton.getText() + "'");

            // Clear input text to ensure mutual exclusion between input_text and voice_text variables
            com.mendhak.gpslogger.ui.components.template.providers.BasicVariableProvider
                .clearInputText(getContext());

            // Ensure GpsLoggingService is running to handle annotation
            if (getActivity() != null) {
                LOG.info("Starting GpsLoggingService to handle annotation");
                getActivity().startService(new android.content.Intent(getActivity(), com.mendhak.gpslogger.GpsLoggingService.class));
            }

            // Post annotation event to GpsLoggingService with template context
            String buttonName = selectedButton.getText();
            int buttonIndex = getButtonIndex(selectedButton);
            String groupName = getButtonGroupName(selectedButton);

            EventBus.getDefault().post(new CommandEvents.Annotate(text.trim(), text.trim(),
                                                                  buttonName, buttonIndex, groupName));
            LOG.info("Posted CommandEvents.Annotate with voice text: '" + text.trim() +
                    "', button: '" + buttonName + "', group: '" + groupName + "'");

            // Clear pending button
            pendingVoiceInputButton = null;
            LOG.info("=== VOICE INPUT PROCESSING COMPLETE ===");
        } else {
            LOG.warn("Voice input result is empty or button is null");
            LOG.warn("pendingVoiceInputButton: " + (pendingVoiceInputButton != null ? "not null" : "null"));
            LOG.warn("text: " + (text != null ? "'" + text + "'" : "null"));
            LOG.warn("text.trim().isEmpty(): " + (text != null ? text.trim().isEmpty() : "N/A"));
            handleVoiceInputError("语音识别结果为空");
        }
    }

    /**
     * Handle voice input error
     */
    private void handleVoiceInputError(String error) {
        LOG.warn("Voice input error: " + error);

        // Show error message and offer fallback
        if (getActivity() != null) {
            try {
                eltos.simpledialogfragment.SimpleDialog.build()
                    .title("语音输入失败")
                    .msgHtml(error + "<br><br>是否使用传统的文本输入方式？")
                    .pos("使用文本输入")
                    .neg("取消")
                    .show(this, "VOICE_INPUT_ERROR");
            } catch (Exception e) {
                LOG.error("Error showing voice input error dialog", e);
                fallbackToTextAnnotation();
            }
        } else {
            fallbackToTextAnnotation();
        }
    }

    /**
     * Handle voice input cancellation
     */
    private void handleVoiceInputCancelled() {
        LOG.debug("Voice input was cancelled by user");
        pendingVoiceInputButton = null;
        // No need to show any message, user cancelled intentionally
    }

    /**
     * Fallback to traditional text annotation
     */
    private void fallbackToTextAnnotation() {
        if (pendingVoiceInputButton != null) {
            selectedButton = pendingVoiceInputButton;
            EventBus.getDefault().post(new CommandEvents.Annotate(pendingVoiceInputButton.getText()));
            pendingVoiceInputButton = null;
        }
    }

    void updateButtons(boolean pending) {

        for (ButtonWrapper btnObj : buttonList) {
            if (btnObj != selectedButton) {
                btnObj.actionButton.setProgress(0);
            } else {
                btnObj.actionButton.setProgress(pending ? 1 : 0);
            }
            btnObj.setText(btnObj.getText());
            btnObj.setColor(btnObj.getColor());
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        LOG.info("=== FRAGMENT ACTIVITY RESULT ===");
        LOG.info("Request code: " + requestCode);
        LOG.info("Result code: " + resultCode);
        LOG.info("Data: " + (data != null ? "not null" : "null"));

        super.onActivityResult(requestCode, resultCode, data);

        // Handle voice input result
        if (voiceInputManager != null) {
            LOG.info("Delegating to VoiceInputManager");
            voiceInputManager.handleActivityResult(requestCode, resultCode, data);
            LOG.info("VoiceInputManager handling completed");
        } else {
            LOG.error("VoiceInputManager is null, cannot handle result");
        }
        LOG.info("=== FRAGMENT ACTIVITY RESULT COMPLETE ===");
    }

    /**
     * Get the index of a button within its group
     */
    private int getButtonIndex(ButtonWrapper button) {
        if (groupManager == null || button == null) {
            return 0;
        }

        String groupId = button.getGroupId();
        if (groupId == null) {
            return 0;
        }

        List<ButtonWrapper> groupButtons = groupManager.getButtonsInGroup(groupId);
        for (int i = 0; i < groupButtons.size(); i++) {
            if (groupButtons.get(i) == button) {
                return i;
            }
        }
        return 0;
    }

    /**
     * Get the name of the group containing the button
     */
    private String getButtonGroupName(ButtonWrapper button) {
        if (groupManager == null || button == null) {
            return "";
        }

        String groupId = button.getGroupId();
        if (groupId == null) {
            return "";
        }

        ButtonGroup group = groupManager.getGroup(groupId);
        return group != null ? group.getName() : "";
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        // Clean up voice input components
        if (voicePermissionManager != null) {
            voicePermissionManager.cleanup();
        }
        if (voiceInputManager != null) {
            voiceInputManager.stopVoiceInput();
        }
    }

    @EventBusHook
    public void onEventMainThread(ServiceEvents.WaitingForLocation waitingForLocation) {
        updateButtons(waitingForLocation.waiting);
    }

    @EventBusHook
    public void onEventMainThread(ServiceEvents.AnnotationStatus annotationStatus) {
        if (annotationStatus.annotationWritten) {
            selectedButton = null;
        }
        updateButtons(!annotationStatus.annotationWritten);
    }

    @EventBusHook
    public void onEventMainThread(ProfileEvents.SwitchToProfile switchToProfile){
        loadSettings();
    }

    @EventBusHook
    public void onEventMainThread(AnnotationEvents.ButtonCountChanged buttonCountChanged) {
        LOG.debug("Button count changed to: " + buttonCountChanged.newButtonCount);
        refreshAnnotationView();
    }

    @EventBusHook
    public void onEventMainThread(AnnotationEvents.LayoutStyleChanged layoutStyleChanged) {
        LOG.debug("Layout style changed: style=" + layoutStyleChanged.layoutStyle +
                 ", spacing=" + layoutStyleChanged.spacingMode +
                 ", view=" + layoutStyleChanged.viewMode);
        refreshAnnotationView();
    }



    /**
     * Show edit dialog for button
     */
    private void showEditDialog(ButtonWrapper wrapper, int position) {
        SimpleFormDialog.build()
                .pos(R.string.ok)
                .neg(R.string.cancel)
                .title(R.string.annotation_edit_button_label)
                .fields(
                        Input.plain("annotations")
                                .hint(R.string.letters_numbers)
                                .inputType(InputType.TYPE_TEXT_FLAG_MULTI_LINE)
                                .text(String.valueOf(wrapper.getText())),
                        ColorField.picker("color")
                                .label(R.string.annotation_edit_button_color).allowCustom(true)
                                .color(Color.parseColor(wrapper.getColor()))
                ).show(this, "btn" + position);
    }

    /**
     * Refresh the annotation view when button count changes
     */
    public void refreshAnnotationView() {
        if (getView() != null) {
            ViewMode currentViewMode = getCurrentViewMode();
            ViewMode newViewMode = ViewMode.fromKey(preferenceHelper.getAnnotationViewMode());

            // Check if we need to switch view modes
            if (currentViewMode != newViewMode) {
                refreshWithAnimation();
            } else {
                // Same view mode, just refresh content
                if (newViewMode == ViewMode.GROUPED && recyclerView != null) {
                    refreshGroupedView();
                } else if (gridLayout != null) {
                    refreshGridView();
                }
            }
        }
    }

    /**
     * Get current view mode based on existing views
     */
    private ViewMode getCurrentViewMode() {
        if (recyclerView != null) {
            // Check if it's grouped view by checking adapter type
            if (recyclerView.getAdapter() instanceof ButtonGroupAdapter) {
                return ViewMode.GROUPED;
            } else {
                // This should not happen since LIST mode is removed
                return ViewMode.GRID;
            }
        } else {
            return ViewMode.GRID;
        }
    }

    /**
     * Refresh with animation when switching view modes
     */
    private void refreshWithAnimation() {
        if (getActivity() == null || getView() == null) return;

        View currentView = getView();

        // Fade out current view
        Animation fadeOut = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_out);
        fadeOut.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {}

            @Override
            public void onAnimationEnd(Animation animation) {
                // Create new view and fade it in
                getActivity().runOnUiThread(() -> {
                    ViewGroup container = (ViewGroup) currentView.getParent();
                    if (container != null) {
                        container.removeView(currentView);
                        View newView = onCreateView(getLayoutInflater(), container, null);
                        container.addView(newView);

                        // Fade in new view
                        Animation fadeIn = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in);
                        newView.startAnimation(fadeIn);
                    }
                });
            }

            @Override
            public void onAnimationRepeat(Animation animation) {}
        });

        currentView.startAnimation(fadeOut);
    }



    /**
     * Refresh grouped view content
     */
    private void refreshGroupedView() {
        // Save current button states before recreating
        saveCurrentButtonStates();

        createButtonsForGroupedView();

        // Initialize group manager
        initializeGroupManager();

        // Load settings for grouped view
        loadSettings();

        // Notify adapter
        if (groupAdapter != null) {
            groupAdapter.updateGroups();
        }

        // Add subtle animation for content refresh
        if (recyclerView != null && getActivity() != null) {
            Animation slideIn = AnimationUtils.loadAnimation(getActivity(), R.anim.slide_in_right);
            recyclerView.startAnimation(slideIn);
        }
    }

    /**
     * Refresh grid view content
     */
    private void refreshGridView() {
        // Save current button states before recreating
        saveCurrentButtonStates();

        createDynamicButtons(getView());

        // Add subtle animation for content refresh
        if (gridLayout != null && getActivity() != null) {
            Animation fadeIn = AnimationUtils.loadAnimation(getActivity(), R.anim.fade_in);
            gridLayout.startAnimation(fadeIn);
        }
    }

    /**
     * Show group edit dialog
     */
    private void showGroupEditDialog(String groupId) {
        try {
            android.util.Log.e("AnnotationViewFragment", "=== showGroupEditDialog CALLED === groupId: " + groupId);

            if (groupManager == null) {
                android.util.Log.e("AnnotationViewFragment", "Cannot show group edit dialog: groupManager is null");
                return;
            }

            if (groupId == null || groupId.isEmpty()) {
                android.util.Log.e("AnnotationViewFragment", "Cannot show group edit dialog: groupId is null or empty");
                return;
            }

            ButtonGroup group = groupManager.getGroup(groupId);
            if (group == null) {
                android.util.Log.e("AnnotationViewFragment", "Cannot show group edit dialog: group not found for id " + groupId);
                return;
            }

            android.util.Log.e("AnnotationViewFragment", "=== CREATING GroupEditDialog === Group: " + group.getName());

            if (context == null || getActivity() == null) {
                LOG.error("Cannot show group edit dialog: context is null");
                return;
            }

            GroupEditDialog dialog = new GroupEditDialog(getActivity(), group, new GroupEditDialog.OnGroupEditListener() {
                @Override
                public void onGroupSaved(ButtonGroup savedGroup) {
                    try {
                        LOG.debug("onGroupSaved called - ID: " + savedGroup.getId() +
                            ", name: " + savedGroup.getName() +
                            ", color: " + savedGroup.getColor() +
                            ", icon: " + savedGroup.getIcon());

                        // Update group in manager
                        groupManager.updateGroup(savedGroup);
                        LOG.debug("Group updated in manager");

                        // Save settings
                        saveSettings();
                        LOG.debug("Settings saved");

                        // Refresh grouped view
                        if (groupAdapter != null) {
                            groupAdapter.updateGroups();
                            LOG.debug("Group adapter updated");
                        } else {
                            LOG.warn("Group adapter is null, cannot refresh");
                        }

                        LOG.debug("Group updated successfully: " + savedGroup.getName());
                    } catch (Exception e) {
                        LOG.error("Error saving group", e);
                    }
                }

                @Override
                public void onGroupCancelled() {
                    LOG.debug("Group edit cancelled");
                }
            });

            android.util.Log.e("AnnotationViewFragment", "=== CALLING dialog.show() ===");
            dialog.show();
            android.util.Log.e("AnnotationViewFragment", "=== dialog.show() COMPLETED ===");
        } catch (Exception e) {
            android.util.Log.e("AnnotationViewFragment", "Error showing group edit dialog", e);
        }
    }

    /**
     * 显示分组按钮管理对话框
     */
    private void showGroupButtonManagementDialog(String groupId) {
        try {
            android.util.Log.d("AnnotationViewFragment", "showGroupButtonManagementDialog called for groupId: " + groupId);

            if (groupManager == null) {
                android.util.Log.e("AnnotationViewFragment", "Cannot show button management dialog: groupManager is null");
                return;
            }

            if (groupId == null || groupId.isEmpty()) {
                android.util.Log.e("AnnotationViewFragment", "Cannot show button management dialog: groupId is null or empty");
                return;
            }

            ButtonGroup group = groupManager.getGroup(groupId);
            if (group == null) {
                android.util.Log.e("AnnotationViewFragment", "Cannot show button management dialog: group not found for id " + groupId);
                return;
            }

            android.util.Log.d("AnnotationViewFragment", "Creating GroupButtonManagementDialog for group: " + group.getName());

            // 🔑 创建并保存对话框引用
            currentManagementDialog = new GroupButtonManagementDialog(
                getActivity(),
                group,
                groupManager,
                new GroupButtonManagementDialog.OnButtonManagementListener() {
                    @Override
                    public void onCreateNewButton(String groupId, String buttonText, String buttonColor, TriggerMode triggerMode) {
                        android.util.Log.d("AnnotationViewFragment", "=== CREATING NEW BUTTON ===");
                        android.util.Log.d("AnnotationViewFragment", "Group ID: " + groupId);
                        android.util.Log.d("AnnotationViewFragment", "Button Text: " + buttonText);
                        android.util.Log.d("AnnotationViewFragment", "Button Color: " + buttonColor);
                        android.util.Log.d("AnnotationViewFragment", "Trigger Mode: " + triggerMode.getDisplayName());

                        // 创建新的ButtonWrapper
                        ButtonWrapper newButton = new ButtonWrapper(null);
                        newButton.setText(buttonText);
                        newButton.setColor(buttonColor);
                        newButton.setGroupId(groupId);
                        newButton.setTriggerMode(triggerMode);

                        // 设置在分组内的顺序
                        List<ButtonWrapper> groupButtons = groupManager.getButtonsInGroup(groupId);
                        newButton.setOrderInGroup(groupButtons.size());

                        android.util.Log.d("AnnotationViewFragment", "Button order in group: " + newButton.getOrderInGroup());

                        // 添加到按钮列表
                        buttonList.add(newButton);
                        android.util.Log.d("AnnotationViewFragment", "Added to buttonList, total buttons: " + buttonList.size());

                        // 确保ButtonGroupManager也知道这个按钮
                        groupManager.addButtonToGroup(newButton);
                        android.util.Log.d("AnnotationViewFragment", "Added to groupManager");

                        // 保存设置并刷新UI
                        saveSettings();
                        if (groupAdapter != null) {
                            groupAdapter.updateGroups();
                            android.util.Log.d("AnnotationViewFragment", "Updated group adapter");
                        }

                        // 🔑 关键修复：刷新管理对话框中的按钮列表
                        if (currentManagementDialog != null) {
                            currentManagementDialog.refreshDialogButtonList();
                            android.util.Log.d("AnnotationViewFragment", "Refreshed management dialog button list");
                        }

                        android.util.Log.d("AnnotationViewFragment", "=== BUTTON CREATION COMPLETED ===");
                    }

                    @Override
                    public void onButtonsRemoved(List<ButtonWrapper> removedButtons) {
                        android.util.Log.d("AnnotationViewFragment", "=== onButtonsRemoved CALLBACK START ===");
                        android.util.Log.d("AnnotationViewFragment", "Removed " + removedButtons.size() + " buttons");

                        for (ButtonWrapper button : removedButtons) {
                            android.util.Log.d("AnnotationViewFragment", "Removed button: " + button.getText());
                        }

                        // 🔑 关键修复：先从buttonList中删除按钮，再保存设置
                        // 这样避免saveSettings()中的setButtons()覆盖删除操作
                        android.util.Log.d("AnnotationViewFragment", "Removing buttons from buttonList...");
                        android.util.Log.d("AnnotationViewFragment", "buttonList size before removal: " + buttonList.size());

                        for (ButtonWrapper removedButton : removedButtons) {
                            boolean removed = buttonList.remove(removedButton);
                            android.util.Log.d("AnnotationViewFragment", "Removed '" + removedButton.getText() +
                                "' from buttonList: " + removed);
                        }

                        android.util.Log.d("AnnotationViewFragment", "buttonList size after removal: " + buttonList.size());

                        // 保存设置（现在buttonList已经同步了）
                        android.util.Log.d("AnnotationViewFragment", "Saving settings...");
                        saveSettings();
                        android.util.Log.d("AnnotationViewFragment", "Settings saved");

                        // 刷新主界面UI
                        if (groupAdapter != null) {
                            android.util.Log.d("AnnotationViewFragment", "Updating group adapter...");
                            groupAdapter.updateGroups();
                            android.util.Log.d("AnnotationViewFragment", "Group adapter updated");
                        } else {
                            android.util.Log.e("AnnotationViewFragment", "groupAdapter is null! Cannot refresh UI");
                        }

                        // 🔑 关键修复：刷新管理对话框中的按钮列表
                        if (currentManagementDialog != null) {
                            android.util.Log.d("AnnotationViewFragment", "Refreshing management dialog button list...");
                            currentManagementDialog.refreshDialogButtonList();
                            android.util.Log.d("AnnotationViewFragment", "Management dialog refreshed");
                        } else {
                            android.util.Log.d("AnnotationViewFragment", "No active management dialog to refresh");
                        }

                        android.util.Log.d("AnnotationViewFragment", "=== onButtonsRemoved CALLBACK END ===");
                    }

                    @Override
                    public void onButtonsMoved(List<ButtonWrapper> movedButtons, String targetGroupId) {
                        android.util.Log.d("AnnotationViewFragment", "onButtonsMoved: " + movedButtons.size() +
                            " buttons to group " + targetGroupId);
                        // 保存设置并刷新UI
                        saveSettings();
                        if (groupAdapter != null) {
                            groupAdapter.updateGroups();
                        }
                    }

                    @Override
                    public void onButtonsReordered(String groupId, List<ButtonWrapper> newOrder) {
                        android.util.Log.d("AnnotationViewFragment", "onButtonsReordered: " + newOrder.size() +
                            " buttons in group " + groupId);
                        // 更新按钮顺序
                        groupManager.reorderButtonsInGroup(groupId, newOrder);
                        // 保存设置并刷新UI
                        saveSettings();
                        if (groupAdapter != null) {
                            groupAdapter.updateGroups();
                        }
                    }

                    @Override
                    public void onManagementCancelled() {
                        android.util.Log.d("AnnotationViewFragment", "Button management cancelled");
                        // 清除对话框引用
                        currentManagementDialog = null;
                    }

                    @Override
                    public void onButtonCreated(ButtonWrapper newButton) {
                        android.util.Log.d("AnnotationViewFragment", "Button created callback: " + newButton.getText());
                        // 这个回调目前不需要特殊处理，因为我们在onCreateNewButton中已经处理了
                    }
                }
            );

            android.util.Log.d("AnnotationViewFragment", "Showing GroupButtonManagementDialog");
            currentManagementDialog.show();

        } catch (Exception e) {
            android.util.Log.e("AnnotationViewFragment", "Error showing group button management dialog", e);
        }
    }

    /**
     * Show group delete confirmation dialog
     */
    private void showGroupDeleteDialog(String groupId) {
        if (groupManager == null) return;

        ButtonGroup group = groupManager.getGroup(groupId);
        if (group == null) return;

        // Don't allow deleting default group
        if (group.isDefault()) {
            android.widget.Toast.makeText(context, "Cannot delete default group", android.widget.Toast.LENGTH_SHORT).show();
            return;
        }

        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(getActivity());
        builder.setTitle("Delete Group");
        builder.setMessage("Are you sure you want to delete \"" + group.getName() + "\"?\n\nAll buttons in this group will be moved to the default group.");
        builder.setPositiveButton("Delete", (dialog, which) -> {
            // Move buttons to default group
            groupManager.moveButtonsToDefaultGroup(groupId);

            // Delete the group
            groupManager.deleteGroup(groupId);

            // Save settings
            saveSettings();

            // Refresh grouped view
            if (groupAdapter != null) {
                groupAdapter.updateGroups();
            }

            LOG.debug("Group deleted: " + group.getName());
        });
        builder.setNegativeButton("Cancel", null);
        builder.show();
    }

    /**
     * Trigger button click programmatically for external control
     * @param buttonIndex 0-based button index
     */
    public void triggerButtonClick(int buttonIndex) {
        LOG.debug("External trigger for button index: {}", buttonIndex);

        if (buttonList == null || buttonIndex < 0 || buttonIndex >= buttonList.size()) {
            LOG.warn("Invalid button index: {} (total buttons: {})", buttonIndex,
                    buttonList != null ? buttonList.size() : 0);
            return;
        }

        try {
            ButtonWrapper wrapper = buttonList.get(buttonIndex);
            if (wrapper != null) {
                LOG.info("Triggering button click for: {}", wrapper.getText());
                onBtnClick(wrapper);
            } else {
                LOG.warn("Button wrapper is null at index: {}", buttonIndex);
            }
        } catch (Exception e) {
            LOG.error("Error triggering button click at index: {}", buttonIndex, e);
        }
    }

    /**
     * Get button count for external control
     */
    public int getButtonCount() {
        return buttonList != null ? buttonList.size() : 0;
    }

    /**
     * Get button text for external control
     */
    public String getButtonText(int buttonIndex) {
        if (buttonList == null || buttonIndex < 0 || buttonIndex >= buttonList.size()) {
            return null;
        }

        ButtonWrapper wrapper = buttonList.get(buttonIndex);
        return wrapper != null ? wrapper.getText() : null;
    }

    /**
     * Start voice input with annotation context
     */
    private void startVoiceInputWithContext(ButtonWrapper wrapper) {
        try {
            // Get current location
            android.location.Location location = Session.getInstance().getCurrentLocationInfo();

            // Extract button information
            String voiceText = wrapper.getText(); // Use button text as voice text
            String buttonName = wrapper.getText();
            int buttonIndex = buttonList.indexOf(wrapper);
            String groupName = "默认"; // Default group name

            // Start voice input with context
            voiceInputManager.startVoiceInputWithContext(voiceText, buttonName, buttonIndex, groupName, location);

        } catch (Exception e) {
            LOG.error("Error starting voice input with context", e);
            // Fallback to simple voice input
            voiceInputManager.startVoiceInput();
        }
    }

    /**
     * Handle audio recording finished
     */
    private void handleAudioRecordingFinished(String audioFilePath) {
        LOG.info("Audio recording finished: {}", audioFilePath);

        // Show success message
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                android.widget.Toast.makeText(getActivity(),
                    "录音已保存: " + new java.io.File(audioFilePath).getName(),
                    android.widget.Toast.LENGTH_LONG).show();
            });
        }

        // Trigger annotation event with audio file path
        triggerAnnotationEvent(audioFilePath);
    }

    /**
     * Handle audio recording error
     */
    private void handleAudioRecordingError(String error) {
        LOG.warn("Audio recording error: {}", error);

        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                android.widget.Toast.makeText(getActivity(),
                    "录音失败: " + error,
                    android.widget.Toast.LENGTH_LONG).show();
            });
        }
    }

    /**
     * Handle audio recording cancelled
     */
    private void handleAudioRecordingCancelled() {
        LOG.debug("Audio recording cancelled");

        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                android.widget.Toast.makeText(getActivity(),
                    "录音已取消",
                    android.widget.Toast.LENGTH_SHORT).show();
            });
        }
    }

    /**
     * Show recording indicator with enhanced visual feedback
     */
    private void showRecordingIndicator(boolean isRecording, String message) {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                // Show recording status in UI
                android.widget.Toast.makeText(getActivity(), message, android.widget.Toast.LENGTH_SHORT).show();

                // Set recording state
                isShowingRecordingIndicator = isRecording;
                if (isRecording && recordingStartTime == 0) {
                    recordingStartTime = System.currentTimeMillis();
                }

                // Update button visual states to indicate recording mode
                updateButtonsRecordingState(isRecording);

                // Provide haptic feedback
                if (hapticFeedbackManager != null) {
                    hapticFeedbackManager.performButtonClickFeedback();
                }
            });
        }
    }

    /**
     * Hide recording indicator and reset visual states
     */
    private void hideRecordingIndicator() {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                // Reset recording state
                isShowingRecordingIndicator = false;
                recordingStartTime = 0;

                // Reset button visual states
                updateButtonsRecordingState(false);

                // Provide haptic feedback
                if (hapticFeedbackManager != null) {
                    hapticFeedbackManager.performButtonClickFeedback();
                }
            });
        }
    }

    /**
     * Update recording duration display
     */
    private void updateRecordingDuration(long durationMs) {
        if (getActivity() != null && getActivity() instanceof GpsMainActivity) {
            // Let the main activity handle the persistent recording status display
            GpsMainActivity mainActivity = (GpsMainActivity) getActivity();
            mainActivity.updateQuickVoiceInputButtonState(true, durationMs);
        }
    }

    /**
     * Update button visual states to indicate recording mode
     */
    private void updateButtonsRecordingState(boolean isRecording) {
        try {
            for (ButtonWrapper wrapper : buttonList) {
                if (wrapper.actionButton != null) {
                    if (isRecording) {
                        // Change button appearance to indicate recording mode
                        wrapper.actionButton.setAlpha(0.7f); // Make buttons semi-transparent
                        wrapper.actionButton.setText("🔴 " + wrapper.text); // Add recording indicator
                    } else {
                        // Restore normal button appearance
                        wrapper.actionButton.setAlpha(1.0f);
                        wrapper.actionButton.setText(wrapper.text); // Remove recording indicator
                    }
                }
            }
        } catch (Exception e) {
            LOG.debug("Error updating button recording states", e);
        }
    }

    /**
     * Trigger annotation event with audio file path
     */
    private void triggerAnnotationEvent(String audioFilePath) {
        try {
            // Create annotation text with audio file reference
            String annotationText = "录音文件: " + new java.io.File(audioFilePath).getName();

            // Get button information from selectedButton
            if (selectedButton != null) {
                String buttonName = selectedButton.getText();
                int buttonIndex = buttonList.indexOf(selectedButton);
                String groupName = "默认"; // Default group name

                // For audio recording mode, voice_text should be "none"
                String voiceText = "none";

                // Trigger the annotation event with complete context
                EventBus.getDefault().post(new CommandEvents.Annotate(
                    annotationText,  // annotation text (for input_text variable)
                    voiceText,       // voice text (should be "none" for recording mode)
                    buttonName,      // button name
                    buttonIndex,     // button index
                    groupName        // group name
                ));

                LOG.debug("Annotation event triggered for audio file: {} with button context: {}, {}, {}",
                         audioFilePath, buttonName, buttonIndex, groupName);
            } else {
                // Fallback if no button context available
                EventBus.getDefault().post(new CommandEvents.Annotate(annotationText, "none", "录音", 0, "默认"));
                LOG.debug("Annotation event triggered for audio file: {} with default context", audioFilePath);
            }

        } catch (Exception e) {
            LOG.error("Error triggering annotation event for audio file", e);
        }
    }
}
